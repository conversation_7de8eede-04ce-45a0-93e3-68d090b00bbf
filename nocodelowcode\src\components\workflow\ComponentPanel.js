import React from 'react';
import { ComponentTypes, ComponentMetadata } from '../../types';

const ComponentPanel = () => {
  const componentTypes = [
    {
      id: ComponentTypes.USER_QUERY,
      ...ComponentMetadata[ComponentTypes.USER_QUERY],
    },
    {
      id: ComponentTypes.KNOWLEDGE_BASE,
      ...ComponentMetadata[ComponentTypes.KNOWLEDGE_BASE],
    },
    {
      id: ComponentTypes.LLM_ENGINE,
      ...ComponentMetadata[ComponentTypes.LLM_ENGINE],
    },
    {
      id: ComponentTypes.OUTPUT,
      ...ComponentMetadata[ComponentTypes.OUTPUT],
    },
  ];

  const handleDragStart = (event, componentType) => {
    event.dataTransfer.setData('application/reactflow', componentType);
    event.dataTransfer.effectAllowed = 'move';

    // Add visual feedback
    event.target.style.opacity = '0.5';
  };

  const handleDragEnd = (event) => {
    event.target.style.opacity = '1';
  };

  const getColorClasses = (color) => {
    const colorMap = {
      blue: 'bg-blue-500 text-white',
      green: 'bg-green-500 text-white',
      purple: 'bg-purple-500 text-white',
      orange: 'bg-orange-500 text-white',
    };
    return colorMap[color] || 'bg-gray-500 text-white';
  };

  return (
    <div className="p-4 space-y-4">
      {componentTypes.map((component) => (
        <div
          key={component.id}
          draggable
          onDragStart={(event) => handleDragStart(event, component.id)}
          onDragEnd={handleDragEnd}
          className="group cursor-move bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105"
        >
          <div className="flex items-start space-x-3">
            <div className={`${getColorClasses(component.color)} p-2 rounded-lg flex-shrink-0 text-lg`}>
              {component.icon}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-semibold text-gray-900 group-hover:text-blue-700">
                {component.name}
              </h3>
              <p className="text-xs text-gray-600 mt-1 leading-relaxed">
                {component.description}
              </p>
            </div>
          </div>

          {/* Drag indicator */}
          <div className="mt-3 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            </div>
          </div>
        </div>
      ))}

      {/* Instructions */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          How to use:
        </h4>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• Drag components to the workspace</li>
          <li>• Configure each component's settings</li>
          <li>• Test your workflow with "Chat with Stack"</li>
        </ul>
      </div>

      {/* Component Guidelines */}
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          Workflow Guidelines:
        </h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Start with a User Query component</li>
          <li>• End with an Output component</li>
          <li>• Knowledge Base is optional but recommended</li>
          <li>• LLM Engine processes the final response</li>
        </ul>
      </div>
    </div>
  );
};

export default ComponentPanel;
