{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\noCodeLowCode\\\\nocodelowcode\\\\src\\\\components\\\\workflow\\\\ReactFlowWorkspace.js\",\n  _s = $RefreshSig$();\nimport React, { useCallback, useRef, useState, useEffect } from 'react';\nimport ReactFlow, { MiniMap, Controls, Background, useNodesState, useEdgesState, addEdge, ConnectionLineType, Panel, Handle, Position } from 'reactflow';\nimport 'reactflow/dist/style.css';\nimport { ComponentMetadata } from '../../types';\n\n// Custom node component with handles for connections\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CustomNode = ({\n  data,\n  selected\n}) => {\n  var _ComponentMetadata$da;\n  const getComponentColor = type => {\n    const colors = {\n      user_query: 'from-blue-500 to-blue-600',\n      knowledge_base: 'from-green-500 to-green-600',\n      llm_engine: 'from-purple-500 to-purple-600',\n      output: 'from-orange-500 to-orange-600',\n      web_search: 'from-indigo-500 to-indigo-600'\n    };\n    return colors[type] || 'from-gray-500 to-gray-600';\n  };\n  const getComponentIcon = type => {\n    const icons = {\n      user_query: '💬',\n      knowledge_base: '📚',\n      llm_engine: '🤖',\n      output: '📤',\n      web_search: '🔍'\n    };\n    return icons[type] || '⚙️';\n  };\n  const getBorderColor = type => {\n    const colors = {\n      user_query: 'border-blue-300',\n      knowledge_base: 'border-green-300',\n      llm_engine: 'border-purple-300',\n      output: 'border-orange-300',\n      web_search: 'border-indigo-300'\n    };\n    return colors[type] || 'border-gray-300';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative bg-white rounded-lg shadow-lg border-2 ${selected ? 'border-blue-500 shadow-blue-200' : getBorderColor(data.component_type)} min-w-[280px] transition-all duration-200 hover:shadow-xl`,\n    children: [data.component_type !== 'user_query' && /*#__PURE__*/_jsxDEV(Handle, {\n      type: \"target\",\n      position: Position.Top,\n      className: \"w-3 h-3 bg-gray-400 border-2 border-white\",\n      style: {\n        top: -6\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-gradient-to-r ${getComponentColor(data.component_type)} text-white p-3 rounded-t-lg`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xl\",\n          children: getComponentIcon(data.component_type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold text-sm\",\n            children: data.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs opacity-90 capitalize\",\n            children: data.component_type.replace('_', ' ')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), Object.keys(data.configuration || {}).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white bg-opacity-20 rounded-full p-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-600 mb-2\",\n        children: ((_ComponentMetadata$da = ComponentMetadata[data.component_type]) === null || _ComponentMetadata$da === void 0 ? void 0 : _ComponentMetadata$da.description) || 'Workflow component'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-500\",\n          children: [\"Status: \", Object.keys(data.configuration || {}).length > 0 ? 'Configured' : 'Not configured']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => data.onSelect && data.onSelect(),\n            className: \"text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded transition-colors\",\n            children: \"Configure\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => data.onDelete && data.onDelete(),\n            className: \"text-xs bg-red-100 hover:bg-red-200 text-red-600 px-2 py-1 rounded transition-colors\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), data.component_type !== 'output' && /*#__PURE__*/_jsxDEV(Handle, {\n      type: \"source\",\n      position: Position.Bottom,\n      className: \"w-3 h-3 bg-gray-400 border-2 border-white\",\n      style: {\n        bottom: -6\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_c = CustomNode;\nconst nodeTypes = {\n  custom: CustomNode\n};\nconst ReactFlowWorkspace = ({\n  components,\n  connections,\n  onComponentSelect,\n  onComponentDelete,\n  onComponentAdd,\n  onConnectionCreate,\n  selectedComponentId,\n  workflow\n}) => {\n  _s();\n  const reactFlowWrapper = useRef(null);\n  const [nodes, setNodes, onNodesChange] = useNodesState([]);\n  const [edges, setEdges, onEdgesChange] = useEdgesState([]);\n  const [reactFlowInstance, setReactFlowInstance] = useState(null);\n\n  // Auto-layout positions for better visual flow\n  const getAutoPosition = (index, type) => {\n    const positions = {\n      user_query: {\n        x: 100,\n        y: 50\n      },\n      knowledge_base: {\n        x: 400,\n        y: 50\n      },\n      web_search: {\n        x: 400,\n        y: 200\n      },\n      llm_engine: {\n        x: 700,\n        y: 125\n      },\n      output: {\n        x: 1000,\n        y: 125\n      }\n    };\n    const basePosition = positions[type] || {\n      x: 100 + index * 300,\n      y: 100\n    };\n    return {\n      x: basePosition.x + (Math.random() - 0.5) * 50,\n      // Add slight randomness\n      y: basePosition.y + (Math.random() - 0.5) * 50\n    };\n  };\n\n  // Convert components to nodes\n  useEffect(() => {\n    const flowNodes = components.map((component, index) => ({\n      id: component.component_id,\n      type: 'custom',\n      position: component.position_x && component.position_y ? {\n        x: component.position_x,\n        y: component.position_y\n      } : getAutoPosition(index, component.component_type),\n      data: {\n        ...component,\n        onDelete: () => onComponentDelete(component.component_id),\n        onSelect: () => onComponentSelect(component.component_id)\n      },\n      selected: selectedComponentId === component.component_id\n    }));\n    setNodes(flowNodes);\n  }, [components, selectedComponentId, onComponentDelete, onComponentSelect, setNodes]);\n\n  // Convert connections to edges with better styling\n  useEffect(() => {\n    const flowEdges = connections.map((connection, index) => {\n      const sourceComponent = components.find(c => c.id === connection.source_component_id);\n      const targetComponent = components.find(c => c.id === connection.target_component_id);\n\n      // Color edges based on source component type\n      const getEdgeColor = sourceType => {\n        const colors = {\n          user_query: '#3b82f6',\n          knowledge_base: '#10b981',\n          llm_engine: '#8b5cf6',\n          web_search: '#6366f1',\n          output: '#f59e0b'\n        };\n        return colors[sourceType] || '#6b7280';\n      };\n      return {\n        id: connection.connection_id || `edge-${index}`,\n        source: connection.source_component_id.toString(),\n        target: connection.target_component_id.toString(),\n        type: 'smoothstep',\n        animated: true,\n        style: {\n          stroke: getEdgeColor(sourceComponent === null || sourceComponent === void 0 ? void 0 : sourceComponent.component_type),\n          strokeWidth: 3,\n          strokeDasharray: '5,5'\n        },\n        markerEnd: {\n          type: 'arrowclosed',\n          color: getEdgeColor(sourceComponent === null || sourceComponent === void 0 ? void 0 : sourceComponent.component_type)\n        },\n        label: `${(sourceComponent === null || sourceComponent === void 0 ? void 0 : sourceComponent.name) || 'Source'} → ${(targetComponent === null || targetComponent === void 0 ? void 0 : targetComponent.name) || 'Target'}`,\n        labelStyle: {\n          fontSize: 10,\n          fontWeight: 600,\n          fill: '#374151'\n        },\n        labelBgStyle: {\n          fill: '#ffffff',\n          fillOpacity: 0.8,\n          rx: 4,\n          ry: 4\n        }\n      };\n    });\n    setEdges(flowEdges);\n  }, [connections, components, setEdges]);\n  const onConnect = useCallback(params => {\n    var _validConnections$sou;\n    // Validate connection logic\n    const sourceComponent = components.find(c => c.component_id === params.source);\n    const targetComponent = components.find(c => c.component_id === params.target);\n    if (!sourceComponent || !targetComponent) return;\n\n    // Prevent self-connections\n    if (params.source === params.target) {\n      alert('Cannot connect a component to itself!');\n      return;\n    }\n\n    // Check for existing connection\n    const existingConnection = connections.find(conn => conn.source_component_id.toString() === params.source && conn.target_component_id.toString() === params.target);\n    if (existingConnection) {\n      alert('Connection already exists!');\n      return;\n    }\n\n    // Validate workflow logic\n    const validConnections = {\n      user_query: ['knowledge_base', 'web_search', 'llm_engine'],\n      knowledge_base: ['llm_engine'],\n      web_search: ['llm_engine'],\n      llm_engine: ['output'],\n      output: []\n    };\n    if (!((_validConnections$sou = validConnections[sourceComponent.component_type]) !== null && _validConnections$sou !== void 0 && _validConnections$sou.includes(targetComponent.component_type))) {\n      alert(`Cannot connect ${sourceComponent.component_type} to ${targetComponent.component_type}. Check workflow logic!`);\n      return;\n    }\n    const newConnection = {\n      connection_id: `conn_${Date.now()}`,\n      source_component_id: parseInt(params.source),\n      target_component_id: parseInt(params.target)\n    };\n    onConnectionCreate(newConnection);\n  }, [onConnectionCreate, components, connections]);\n  const onDragOver = useCallback(event => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'move';\n  }, []);\n  const onDrop = useCallback(event => {\n    var _ComponentMetadata$co;\n    event.preventDefault();\n    const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();\n    const componentType = event.dataTransfer.getData('application/reactflow');\n    if (typeof componentType === 'undefined' || !componentType) {\n      return;\n    }\n    const position = reactFlowInstance.project({\n      x: event.clientX - reactFlowBounds.left,\n      y: event.clientY - reactFlowBounds.top\n    });\n\n    // Check if component type already exists (for unique components)\n    const uniqueComponents = ['user_query', 'output'];\n    if (uniqueComponents.includes(componentType)) {\n      const existingComponent = components.find(c => c.component_type === componentType);\n      if (existingComponent) {\n        alert(`Only one ${componentType.replace('_', ' ')} component is allowed per workflow!`);\n        return;\n      }\n    }\n    const newComponent = {\n      component_id: `${componentType}_${Date.now()}`,\n      component_type: componentType,\n      name: ((_ComponentMetadata$co = ComponentMetadata[componentType]) === null || _ComponentMetadata$co === void 0 ? void 0 : _ComponentMetadata$co.name) || componentType,\n      position_x: position.x,\n      position_y: position.y,\n      configuration: {}\n    };\n    onComponentAdd(newComponent);\n  }, [reactFlowInstance, onComponentAdd, components]);\n  const onNodeClick = useCallback((event, node) => {\n    onComponentSelect(node.id);\n  }, [onComponentSelect]);\n  const onNodeDragStop = useCallback((event, node) => {\n    // Update component position\n    const component = components.find(c => c.component_id === node.id);\n    if (component) {\n      console.log('Node moved:', node.id, node.position);\n      // You can add position update logic here if needed\n    }\n  }, [components]);\n  const connectionLineStyle = {\n    strokeWidth: 3,\n    stroke: '#3b82f6',\n    strokeDasharray: '5,5'\n  };\n  const defaultEdgeOptions = {\n    style: {\n      strokeWidth: 3,\n      stroke: '#3b82f6'\n    },\n    type: 'smoothstep',\n    markerEnd: {\n      type: 'arrowclosed',\n      color: '#3b82f6'\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-full bg-gray-50\",\n    ref: reactFlowWrapper,\n    children: /*#__PURE__*/_jsxDEV(ReactFlow, {\n      nodes: nodes,\n      edges: edges,\n      onNodesChange: onNodesChange,\n      onEdgesChange: onEdgesChange,\n      onConnect: onConnect,\n      onInit: setReactFlowInstance,\n      onDrop: onDrop,\n      onDragOver: onDragOver,\n      onNodeClick: onNodeClick,\n      onNodeDragStop: onNodeDragStop,\n      nodeTypes: nodeTypes,\n      connectionLineType: ConnectionLineType.SmoothStep,\n      connectionLineStyle: connectionLineStyle,\n      defaultEdgeOptions: defaultEdgeOptions,\n      fitView: true,\n      attributionPosition: \"bottom-left\",\n      proOptions: {\n        hideAttribution: true\n      },\n      className: \"bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(MiniMap, {\n        nodeStrokeColor: n => {\n          var _nodes$find;\n          const nodeData = (_nodes$find = nodes.find(node => node.id === n.id)) === null || _nodes$find === void 0 ? void 0 : _nodes$find.data;\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'user_query') return '#3b82f6';\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'knowledge_base') return '#10b981';\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'llm_engine') return '#8b5cf6';\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'output') return '#f59e0b';\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'web_search') return '#6366f1';\n          return '#6b7280';\n        },\n        nodeColor: n => {\n          var _nodes$find2;\n          const nodeData = (_nodes$find2 = nodes.find(node => node.id === n.id)) === null || _nodes$find2 === void 0 ? void 0 : _nodes$find2.data;\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'user_query') return '#3b82f6';\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'knowledge_base') return '#10b981';\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'llm_engine') return '#8b5cf6';\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'output') return '#f59e0b';\n          if ((nodeData === null || nodeData === void 0 ? void 0 : nodeData.component_type) === 'web_search') return '#6366f1';\n          return '#6b7280';\n        },\n        nodeBorderRadius: 8,\n        className: \"bg-white border border-gray-200 rounded-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Controls, {\n        className: \"bg-white border border-gray-200 rounded-lg shadow-lg\",\n        showZoom: true,\n        showFitView: true,\n        showInteractive: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Background, {\n        color: \"#e5e7eb\",\n        gap: 20,\n        size: 1,\n        variant: \"dots\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Panel, {\n        position: \"top-left\",\n        className: \"bg-white rounded-lg shadow-lg border border-gray-200 p-4 m-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white text-sm font-bold\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-bold text-gray-900 text-sm\",\n              children: (workflow === null || workflow === void 0 ? void 0 : workflow.name) || 'AI Workflow Builder'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-600\",\n              children: [components.length, \" Components \\u2022 \", connections.length, \" Connections\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 pt-3 border-t border-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: components.some(c => c.component_type === 'user_query') && components.some(c => c.component_type === 'output') ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 text-green-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs font-medium\",\n                children: \"Valid Workflow\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 text-amber-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs font-medium\",\n                children: \"Incomplete\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this), components.length === 0 && /*#__PURE__*/_jsxDEV(Panel, {\n        position: \"center\",\n        className: \"pointer-events-none\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-8 rounded-xl shadow-2xl text-center max-w-md border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-3xl\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-3\",\n            children: \"Build Your AI Workflow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6 leading-relaxed\",\n            children: \"Drag components from the left panel and connect them to create powerful AI-driven workflows\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-blue-800 font-medium mb-2\",\n              children: \"\\uD83D\\uDCA1 Quick Start Guide:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-blue-700 space-y-1 text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"1. Add a User Query component\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"2. Add Knowledge Base or Web Search\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"3. Add an LLM Engine component\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"4. Add an Output component\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"5. Connect them in order\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 365,\n    columnNumber: 5\n  }, this);\n};\n_s(ReactFlowWorkspace, \"NlInxrf7MzjD4JLSqEa9Ea0uGMY=\", false, function () {\n  return [useNodesState, useEdgesState];\n});\n_c2 = ReactFlowWorkspace;\nexport default ReactFlowWorkspace;\nvar _c, _c2;\n$RefreshReg$(_c, \"CustomNode\");\n$RefreshReg$(_c2, \"ReactFlowWorkspace\");", "map": {"version": 3, "names": ["React", "useCallback", "useRef", "useState", "useEffect", "ReactFlow", "MiniMap", "Controls", "Background", "useNodesState", "useEdgesState", "addEdge", "ConnectionLineType", "Panel", "<PERSON><PERSON>", "Position", "ComponentMetadata", "jsxDEV", "_jsxDEV", "CustomNode", "data", "selected", "_ComponentMetadata$da", "getComponentColor", "type", "colors", "user_query", "knowledge_base", "llm_engine", "output", "web_search", "getComponentIcon", "icons", "getBorderColor", "className", "component_type", "children", "position", "Top", "style", "top", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "replace", "Object", "keys", "configuration", "length", "fill", "viewBox", "fillRule", "d", "clipRule", "description", "onClick", "onSelect", "onDelete", "Bottom", "bottom", "_c", "nodeTypes", "custom", "ReactFlowWorkspace", "components", "connections", "onComponentSelect", "onComponentDelete", "onComponentAdd", "onConnectionCreate", "selectedComponentId", "workflow", "_s", "reactFlowWrapper", "nodes", "setNodes", "onNodesChange", "edges", "set<PERSON><PERSON>", "onEdgesChange", "reactFlowInstance", "setReactFlowInstance", "getAutoPosition", "index", "positions", "x", "y", "basePosition", "Math", "random", "flowNodes", "map", "component", "id", "component_id", "position_x", "position_y", "flowEdges", "connection", "sourceComponent", "find", "c", "source_component_id", "targetComponent", "target_component_id", "getEdgeColor", "sourceType", "connection_id", "source", "toString", "target", "animated", "stroke", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "markerEnd", "color", "label", "labelStyle", "fontSize", "fontWeight", "labelBgStyle", "fillOpacity", "rx", "ry", "onConnect", "params", "_validConnections$sou", "alert", "existingConnection", "conn", "validConnections", "includes", "newConnection", "Date", "now", "parseInt", "onDragOver", "event", "preventDefault", "dataTransfer", "dropEffect", "onDrop", "_ComponentMetadata$co", "reactFlowBounds", "current", "getBoundingClientRect", "componentType", "getData", "project", "clientX", "left", "clientY", "uniqueComponents", "existingComponent", "newComponent", "onNodeClick", "node", "onNodeDragStop", "console", "log", "connectionLineStyle", "defaultEdgeOptions", "ref", "onInit", "connectionLineType", "SmoothStep", "<PERSON><PERSON><PERSON><PERSON>", "attributionPosition", "proOptions", "hideAttribution", "nodeStrokeColor", "n", "_nodes$find", "nodeData", "nodeColor", "_nodes$find2", "nodeBorderRadius", "showZoom", "showFitView", "showInteractive", "gap", "size", "variant", "some", "_c2", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/noCodeLowCode/nocodelowcode/src/components/workflow/ReactFlowWorkspace.js"], "sourcesContent": ["import React, { useCallback, useRef, useState, useEffect } from 'react';\nimport ReactFlow, {\n  MiniMap,\n  Controls,\n  Background,\n  useNodesState,\n  useEdgesState,\n  addEdge,\n  ConnectionLineType,\n  Panel,\n  Handle,\n  Position,\n} from 'reactflow';\nimport 'reactflow/dist/style.css';\n\nimport { ComponentMetadata } from '../../types';\n\n// Custom node component with handles for connections\nconst CustomNode = ({ data, selected }) => {\n  const getComponentColor = (type) => {\n    const colors = {\n      user_query: 'from-blue-500 to-blue-600',\n      knowledge_base: 'from-green-500 to-green-600',\n      llm_engine: 'from-purple-500 to-purple-600',\n      output: 'from-orange-500 to-orange-600',\n      web_search: 'from-indigo-500 to-indigo-600'\n    };\n    return colors[type] || 'from-gray-500 to-gray-600';\n  };\n\n  const getComponentIcon = (type) => {\n    const icons = {\n      user_query: '💬',\n      knowledge_base: '📚',\n      llm_engine: '🤖',\n      output: '📤',\n      web_search: '🔍'\n    };\n    return icons[type] || '⚙️';\n  };\n\n  const getBorderColor = (type) => {\n    const colors = {\n      user_query: 'border-blue-300',\n      knowledge_base: 'border-green-300',\n      llm_engine: 'border-purple-300',\n      output: 'border-orange-300',\n      web_search: 'border-indigo-300'\n    };\n    return colors[type] || 'border-gray-300';\n  };\n\n  return (\n    <div className={`relative bg-white rounded-lg shadow-lg border-2 ${\n      selected ? 'border-blue-500 shadow-blue-200' : getBorderColor(data.component_type)\n    } min-w-[280px] transition-all duration-200 hover:shadow-xl`}>\n\n      {/* Input Handle - Top */}\n      {data.component_type !== 'user_query' && (\n        <Handle\n          type=\"target\"\n          position={Position.Top}\n          className=\"w-3 h-3 bg-gray-400 border-2 border-white\"\n          style={{ top: -6 }}\n        />\n      )}\n\n      {/* Header with gradient background */}\n      <div className={`bg-gradient-to-r ${getComponentColor(data.component_type)} text-white p-3 rounded-t-lg`}>\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"text-xl\">\n            {getComponentIcon(data.component_type)}\n          </div>\n          <div className=\"flex-1\">\n            <div className=\"font-semibold text-sm\">{data.name}</div>\n            <div className=\"text-xs opacity-90 capitalize\">\n              {data.component_type.replace('_', ' ')}\n            </div>\n          </div>\n          {Object.keys(data.configuration || {}).length > 0 && (\n            <div className=\"bg-white bg-opacity-20 rounded-full p-1\">\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Content area */}\n      <div className=\"p-3\">\n        <div className=\"text-xs text-gray-600 mb-2\">\n          {ComponentMetadata[data.component_type]?.description || 'Workflow component'}\n        </div>\n\n        {/* Configuration status */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-xs text-gray-500\">\n            Status: {Object.keys(data.configuration || {}).length > 0 ? 'Configured' : 'Not configured'}\n          </div>\n          <div className=\"flex space-x-1\">\n            <button\n              onClick={() => data.onSelect && data.onSelect()}\n              className=\"text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded transition-colors\"\n            >\n              Configure\n            </button>\n            <button\n              onClick={() => data.onDelete && data.onDelete()}\n              className=\"text-xs bg-red-100 hover:bg-red-200 text-red-600 px-2 py-1 rounded transition-colors\"\n            >\n              Delete\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Output Handle - Bottom */}\n      {data.component_type !== 'output' && (\n        <Handle\n          type=\"source\"\n          position={Position.Bottom}\n          className=\"w-3 h-3 bg-gray-400 border-2 border-white\"\n          style={{ bottom: -6 }}\n        />\n      )}\n    </div>\n  );\n};\n\nconst nodeTypes = {\n  custom: CustomNode,\n};\n\nconst ReactFlowWorkspace = ({\n  components,\n  connections,\n  onComponentSelect,\n  onComponentDelete,\n  onComponentAdd,\n  onConnectionCreate,\n  selectedComponentId,\n  workflow\n}) => {\n  const reactFlowWrapper = useRef(null);\n  const [nodes, setNodes, onNodesChange] = useNodesState([]);\n  const [edges, setEdges, onEdgesChange] = useEdgesState([]);\n  const [reactFlowInstance, setReactFlowInstance] = useState(null);\n\n  // Auto-layout positions for better visual flow\n  const getAutoPosition = (index, type) => {\n    const positions = {\n      user_query: { x: 100, y: 50 },\n      knowledge_base: { x: 400, y: 50 },\n      web_search: { x: 400, y: 200 },\n      llm_engine: { x: 700, y: 125 },\n      output: { x: 1000, y: 125 }\n    };\n\n    const basePosition = positions[type] || { x: 100 + (index * 300), y: 100 };\n    return {\n      x: basePosition.x + (Math.random() - 0.5) * 50, // Add slight randomness\n      y: basePosition.y + (Math.random() - 0.5) * 50\n    };\n  };\n\n  // Convert components to nodes\n  useEffect(() => {\n    const flowNodes = components.map((component, index) => ({\n      id: component.component_id,\n      type: 'custom',\n      position: component.position_x && component.position_y\n        ? { x: component.position_x, y: component.position_y }\n        : getAutoPosition(index, component.component_type),\n      data: {\n        ...component,\n        onDelete: () => onComponentDelete(component.component_id),\n        onSelect: () => onComponentSelect(component.component_id),\n      },\n      selected: selectedComponentId === component.component_id,\n    }));\n    setNodes(flowNodes);\n  }, [components, selectedComponentId, onComponentDelete, onComponentSelect, setNodes]);\n\n  // Convert connections to edges with better styling\n  useEffect(() => {\n    const flowEdges = connections.map((connection, index) => {\n      const sourceComponent = components.find(c => c.id === connection.source_component_id);\n      const targetComponent = components.find(c => c.id === connection.target_component_id);\n\n      // Color edges based on source component type\n      const getEdgeColor = (sourceType) => {\n        const colors = {\n          user_query: '#3b82f6',\n          knowledge_base: '#10b981',\n          llm_engine: '#8b5cf6',\n          web_search: '#6366f1',\n          output: '#f59e0b'\n        };\n        return colors[sourceType] || '#6b7280';\n      };\n\n      return {\n        id: connection.connection_id || `edge-${index}`,\n        source: connection.source_component_id.toString(),\n        target: connection.target_component_id.toString(),\n        type: 'smoothstep',\n        animated: true,\n        style: {\n          stroke: getEdgeColor(sourceComponent?.component_type),\n          strokeWidth: 3,\n          strokeDasharray: '5,5'\n        },\n        markerEnd: {\n          type: 'arrowclosed',\n          color: getEdgeColor(sourceComponent?.component_type),\n        },\n        label: `${sourceComponent?.name || 'Source'} → ${targetComponent?.name || 'Target'}`,\n        labelStyle: {\n          fontSize: 10,\n          fontWeight: 600,\n          fill: '#374151'\n        },\n        labelBgStyle: {\n          fill: '#ffffff',\n          fillOpacity: 0.8,\n          rx: 4,\n          ry: 4\n        },\n      };\n    });\n    setEdges(flowEdges);\n  }, [connections, components, setEdges]);\n\n  const onConnect = useCallback(\n    (params) => {\n      // Validate connection logic\n      const sourceComponent = components.find(c => c.component_id === params.source);\n      const targetComponent = components.find(c => c.component_id === params.target);\n\n      if (!sourceComponent || !targetComponent) return;\n\n      // Prevent self-connections\n      if (params.source === params.target) {\n        alert('Cannot connect a component to itself!');\n        return;\n      }\n\n      // Check for existing connection\n      const existingConnection = connections.find(\n        conn => conn.source_component_id.toString() === params.source &&\n                conn.target_component_id.toString() === params.target\n      );\n\n      if (existingConnection) {\n        alert('Connection already exists!');\n        return;\n      }\n\n      // Validate workflow logic\n      const validConnections = {\n        user_query: ['knowledge_base', 'web_search', 'llm_engine'],\n        knowledge_base: ['llm_engine'],\n        web_search: ['llm_engine'],\n        llm_engine: ['output'],\n        output: []\n      };\n\n      if (!validConnections[sourceComponent.component_type]?.includes(targetComponent.component_type)) {\n        alert(`Cannot connect ${sourceComponent.component_type} to ${targetComponent.component_type}. Check workflow logic!`);\n        return;\n      }\n\n      const newConnection = {\n        connection_id: `conn_${Date.now()}`,\n        source_component_id: parseInt(params.source),\n        target_component_id: parseInt(params.target),\n      };\n\n      onConnectionCreate(newConnection);\n    },\n    [onConnectionCreate, components, connections]\n  );\n\n  const onDragOver = useCallback((event) => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'move';\n  }, []);\n\n  const onDrop = useCallback(\n    (event) => {\n      event.preventDefault();\n\n      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();\n      const componentType = event.dataTransfer.getData('application/reactflow');\n\n      if (typeof componentType === 'undefined' || !componentType) {\n        return;\n      }\n\n      const position = reactFlowInstance.project({\n        x: event.clientX - reactFlowBounds.left,\n        y: event.clientY - reactFlowBounds.top,\n      });\n\n      // Check if component type already exists (for unique components)\n      const uniqueComponents = ['user_query', 'output'];\n      if (uniqueComponents.includes(componentType)) {\n        const existingComponent = components.find(c => c.component_type === componentType);\n        if (existingComponent) {\n          alert(`Only one ${componentType.replace('_', ' ')} component is allowed per workflow!`);\n          return;\n        }\n      }\n\n      const newComponent = {\n        component_id: `${componentType}_${Date.now()}`,\n        component_type: componentType,\n        name: ComponentMetadata[componentType]?.name || componentType,\n        position_x: position.x,\n        position_y: position.y,\n        configuration: {},\n      };\n\n      onComponentAdd(newComponent);\n    },\n    [reactFlowInstance, onComponentAdd, components]\n  );\n\n  const onNodeClick = useCallback(\n    (event, node) => {\n      onComponentSelect(node.id);\n    },\n    [onComponentSelect]\n  );\n\n  const onNodeDragStop = useCallback(\n    (event, node) => {\n      // Update component position\n      const component = components.find(c => c.component_id === node.id);\n      if (component) {\n        console.log('Node moved:', node.id, node.position);\n        // You can add position update logic here if needed\n      }\n    },\n    [components]\n  );\n\n  const connectionLineStyle = {\n    strokeWidth: 3,\n    stroke: '#3b82f6',\n    strokeDasharray: '5,5',\n  };\n\n  const defaultEdgeOptions = {\n    style: { strokeWidth: 3, stroke: '#3b82f6' },\n    type: 'smoothstep',\n    markerEnd: {\n      type: 'arrowclosed',\n      color: '#3b82f6',\n    },\n  };\n\n  return (\n    <div className=\"w-full h-full bg-gray-50\" ref={reactFlowWrapper}>\n      <ReactFlow\n        nodes={nodes}\n        edges={edges}\n        onNodesChange={onNodesChange}\n        onEdgesChange={onEdgesChange}\n        onConnect={onConnect}\n        onInit={setReactFlowInstance}\n        onDrop={onDrop}\n        onDragOver={onDragOver}\n        onNodeClick={onNodeClick}\n        onNodeDragStop={onNodeDragStop}\n        nodeTypes={nodeTypes}\n        connectionLineType={ConnectionLineType.SmoothStep}\n        connectionLineStyle={connectionLineStyle}\n        defaultEdgeOptions={defaultEdgeOptions}\n        fitView\n        attributionPosition=\"bottom-left\"\n        proOptions={{ hideAttribution: true }}\n        className=\"bg-gray-50\"\n      >\n        <MiniMap\n          nodeStrokeColor={(n) => {\n            const nodeData = nodes.find(node => node.id === n.id)?.data;\n            if (nodeData?.component_type === 'user_query') return '#3b82f6';\n            if (nodeData?.component_type === 'knowledge_base') return '#10b981';\n            if (nodeData?.component_type === 'llm_engine') return '#8b5cf6';\n            if (nodeData?.component_type === 'output') return '#f59e0b';\n            if (nodeData?.component_type === 'web_search') return '#6366f1';\n            return '#6b7280';\n          }}\n          nodeColor={(n) => {\n            const nodeData = nodes.find(node => node.id === n.id)?.data;\n            if (nodeData?.component_type === 'user_query') return '#3b82f6';\n            if (nodeData?.component_type === 'knowledge_base') return '#10b981';\n            if (nodeData?.component_type === 'llm_engine') return '#8b5cf6';\n            if (nodeData?.component_type === 'output') return '#f59e0b';\n            if (nodeData?.component_type === 'web_search') return '#6366f1';\n            return '#6b7280';\n          }}\n          nodeBorderRadius={8}\n          className=\"bg-white border border-gray-200 rounded-lg\"\n        />\n        <Controls\n          className=\"bg-white border border-gray-200 rounded-lg shadow-lg\"\n          showZoom={true}\n          showFitView={true}\n          showInteractive={true}\n        />\n        <Background\n          color=\"#e5e7eb\"\n          gap={20}\n          size={1}\n          variant=\"dots\"\n        />\n\n        <Panel position=\"top-left\" className=\"bg-white rounded-lg shadow-lg border border-gray-200 p-4 m-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white text-sm font-bold\">AI</span>\n            </div>\n            <div>\n              <h3 className=\"font-bold text-gray-900 text-sm\">\n                {workflow?.name || 'AI Workflow Builder'}\n              </h3>\n              <div className=\"text-xs text-gray-600\">\n                {components.length} Components • {connections.length} Connections\n              </div>\n            </div>\n          </div>\n\n          {/* Workflow validation status */}\n          <div className=\"mt-3 pt-3 border-t border-gray-100\">\n            <div className=\"flex items-center space-x-2\">\n              {components.some(c => c.component_type === 'user_query') &&\n               components.some(c => c.component_type === 'output') ? (\n                <div className=\"flex items-center space-x-1 text-green-600\">\n                  <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span className=\"text-xs font-medium\">Valid Workflow</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-1 text-amber-600\">\n                  <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span className=\"text-xs font-medium\">Incomplete</span>\n                </div>\n              )}\n            </div>\n          </div>\n        </Panel>\n\n        {components.length === 0 && (\n          <Panel position=\"center\" className=\"pointer-events-none\">\n            <div className=\"bg-white p-8 rounded-xl shadow-2xl text-center max-w-md border border-gray-200\">\n              <div className=\"w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                <span className=\"text-3xl\">🚀</span>\n              </div>\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                Build Your AI Workflow\n              </h3>\n              <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                Drag components from the left panel and connect them to create powerful AI-driven workflows\n              </p>\n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <div className=\"text-sm text-blue-800 font-medium mb-2\">\n                  💡 Quick Start Guide:\n                </div>\n                <div className=\"text-xs text-blue-700 space-y-1 text-left\">\n                  <div>1. Add a User Query component</div>\n                  <div>2. Add Knowledge Base or Web Search</div>\n                  <div>3. Add an LLM Engine component</div>\n                  <div>4. Add an Output component</div>\n                  <div>5. Connect them in order</div>\n                </div>\n              </div>\n            </div>\n          </Panel>\n        )}\n      </ReactFlow>\n    </div>\n  );\n};\n\nexport default ReactFlowWorkspace;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACvE,OAAOC,SAAS,IACdC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,aAAa,EACbC,OAAO,EACPC,kBAAkB,EAClBC,KAAK,EACLC,MAAM,EACNC,QAAQ,QACH,WAAW;AAClB,OAAO,0BAA0B;AAEjC,SAASC,iBAAiB,QAAQ,aAAa;;AAE/C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,UAAU,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAA,IAAAC,qBAAA;EACzC,MAAMC,iBAAiB,GAAIC,IAAI,IAAK;IAClC,MAAMC,MAAM,GAAG;MACbC,UAAU,EAAE,2BAA2B;MACvCC,cAAc,EAAE,6BAA6B;MAC7CC,UAAU,EAAE,+BAA+B;MAC3CC,MAAM,EAAE,+BAA+B;MACvCC,UAAU,EAAE;IACd,CAAC;IACD,OAAOL,MAAM,CAACD,IAAI,CAAC,IAAI,2BAA2B;EACpD,CAAC;EAED,MAAMO,gBAAgB,GAAIP,IAAI,IAAK;IACjC,MAAMQ,KAAK,GAAG;MACZN,UAAU,EAAE,IAAI;MAChBC,cAAc,EAAE,IAAI;MACpBC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE;IACd,CAAC;IACD,OAAOE,KAAK,CAACR,IAAI,CAAC,IAAI,IAAI;EAC5B,CAAC;EAED,MAAMS,cAAc,GAAIT,IAAI,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACbC,UAAU,EAAE,iBAAiB;MAC7BC,cAAc,EAAE,kBAAkB;MAClCC,UAAU,EAAE,mBAAmB;MAC/BC,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE;IACd,CAAC;IACD,OAAOL,MAAM,CAACD,IAAI,CAAC,IAAI,iBAAiB;EAC1C,CAAC;EAED,oBACEN,OAAA;IAAKgB,SAAS,EAAE,mDACdb,QAAQ,GAAG,iCAAiC,GAAGY,cAAc,CAACb,IAAI,CAACe,cAAc,CAAC,4DACvB;IAAAC,QAAA,GAG1DhB,IAAI,CAACe,cAAc,KAAK,YAAY,iBACnCjB,OAAA,CAACJ,MAAM;MACLU,IAAI,EAAC,QAAQ;MACba,QAAQ,EAAEtB,QAAQ,CAACuB,GAAI;MACvBJ,SAAS,EAAC,2CAA2C;MACrDK,KAAK,EAAE;QAAEC,GAAG,EAAE,CAAC;MAAE;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CACF,eAGD1B,OAAA;MAAKgB,SAAS,EAAE,oBAAoBX,iBAAiB,CAACH,IAAI,CAACe,cAAc,CAAC,8BAA+B;MAAAC,QAAA,eACvGlB,OAAA;QAAKgB,SAAS,EAAC,6BAA6B;QAAAE,QAAA,gBAC1ClB,OAAA;UAAKgB,SAAS,EAAC,SAAS;UAAAE,QAAA,EACrBL,gBAAgB,CAACX,IAAI,CAACe,cAAc;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACN1B,OAAA;UAAKgB,SAAS,EAAC,QAAQ;UAAAE,QAAA,gBACrBlB,OAAA;YAAKgB,SAAS,EAAC,uBAAuB;YAAAE,QAAA,EAAEhB,IAAI,CAACyB;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxD1B,OAAA;YAAKgB,SAAS,EAAC,+BAA+B;YAAAE,QAAA,EAC3ChB,IAAI,CAACe,cAAc,CAACW,OAAO,CAAC,GAAG,EAAE,GAAG;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLG,MAAM,CAACC,IAAI,CAAC5B,IAAI,CAAC6B,aAAa,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,iBAC/ChC,OAAA;UAAKgB,SAAS,EAAC,yCAAyC;UAAAE,QAAA,eACtDlB,OAAA;YAAKgB,SAAS,EAAC,SAAS;YAACiB,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAhB,QAAA,eAC9DlB,OAAA;cAAMmC,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKgB,SAAS,EAAC,KAAK;MAAAE,QAAA,gBAClBlB,OAAA;QAAKgB,SAAS,EAAC,4BAA4B;QAAAE,QAAA,EACxC,EAAAd,qBAAA,GAAAN,iBAAiB,CAACI,IAAI,CAACe,cAAc,CAAC,cAAAb,qBAAA,uBAAtCA,qBAAA,CAAwCkC,WAAW,KAAI;MAAoB;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAGN1B,OAAA;QAAKgB,SAAS,EAAC,mCAAmC;QAAAE,QAAA,gBAChDlB,OAAA;UAAKgB,SAAS,EAAC,uBAAuB;UAAAE,QAAA,GAAC,UAC7B,EAACW,MAAM,CAACC,IAAI,CAAC5B,IAAI,CAAC6B,aAAa,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,gBAAgB;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eACN1B,OAAA;UAAKgB,SAAS,EAAC,gBAAgB;UAAAE,QAAA,gBAC7BlB,OAAA;YACEuC,OAAO,EAAEA,CAAA,KAAMrC,IAAI,CAACsC,QAAQ,IAAItC,IAAI,CAACsC,QAAQ,CAAC,CAAE;YAChDxB,SAAS,EAAC,2EAA2E;YAAAE,QAAA,EACtF;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1B,OAAA;YACEuC,OAAO,EAAEA,CAAA,KAAMrC,IAAI,CAACuC,QAAQ,IAAIvC,IAAI,CAACuC,QAAQ,CAAC,CAAE;YAChDzB,SAAS,EAAC,sFAAsF;YAAAE,QAAA,EACjG;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxB,IAAI,CAACe,cAAc,KAAK,QAAQ,iBAC/BjB,OAAA,CAACJ,MAAM;MACLU,IAAI,EAAC,QAAQ;MACba,QAAQ,EAAEtB,QAAQ,CAAC6C,MAAO;MAC1B1B,SAAS,EAAC,2CAA2C;MACrDK,KAAK,EAAE;QAAEsB,MAAM,EAAE,CAAC;MAAE;IAAE;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACkB,EAAA,GA9GI3C,UAAU;AAgHhB,MAAM4C,SAAS,GAAG;EAChBC,MAAM,EAAE7C;AACV,CAAC;AAED,MAAM8C,kBAAkB,GAAGA,CAAC;EAC1BC,UAAU;EACVC,WAAW;EACXC,iBAAiB;EACjBC,iBAAiB;EACjBC,cAAc;EACdC,kBAAkB;EAClBC,mBAAmB;EACnBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,gBAAgB,GAAGzE,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,EAAEC,aAAa,CAAC,GAAGrE,aAAa,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsE,KAAK,EAAEC,QAAQ,EAAEC,aAAa,CAAC,GAAGvE,aAAa,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAMiF,eAAe,GAAGA,CAACC,KAAK,EAAE7D,IAAI,KAAK;IACvC,MAAM8D,SAAS,GAAG;MAChB5D,UAAU,EAAE;QAAE6D,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAG,CAAC;MAC7B7D,cAAc,EAAE;QAAE4D,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAG,CAAC;MACjC1D,UAAU,EAAE;QAAEyD,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC;MAC9B5D,UAAU,EAAE;QAAE2D,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC;MAC9B3D,MAAM,EAAE;QAAE0D,CAAC,EAAE,IAAI;QAAEC,CAAC,EAAE;MAAI;IAC5B,CAAC;IAED,MAAMC,YAAY,GAAGH,SAAS,CAAC9D,IAAI,CAAC,IAAI;MAAE+D,CAAC,EAAE,GAAG,GAAIF,KAAK,GAAG,GAAI;MAAEG,CAAC,EAAE;IAAI,CAAC;IAC1E,OAAO;MACLD,CAAC,EAAEE,YAAY,CAACF,CAAC,GAAG,CAACG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE;MAAE;MAChDH,CAAC,EAAEC,YAAY,CAACD,CAAC,GAAG,CAACE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;IAC9C,CAAC;EACH,CAAC;;EAED;EACAvF,SAAS,CAAC,MAAM;IACd,MAAMwF,SAAS,GAAG1B,UAAU,CAAC2B,GAAG,CAAC,CAACC,SAAS,EAAET,KAAK,MAAM;MACtDU,EAAE,EAAED,SAAS,CAACE,YAAY;MAC1BxE,IAAI,EAAE,QAAQ;MACda,QAAQ,EAAEyD,SAAS,CAACG,UAAU,IAAIH,SAAS,CAACI,UAAU,GAClD;QAAEX,CAAC,EAAEO,SAAS,CAACG,UAAU;QAAET,CAAC,EAAEM,SAAS,CAACI;MAAW,CAAC,GACpDd,eAAe,CAACC,KAAK,EAAES,SAAS,CAAC3D,cAAc,CAAC;MACpDf,IAAI,EAAE;QACJ,GAAG0E,SAAS;QACZnC,QAAQ,EAAEA,CAAA,KAAMU,iBAAiB,CAACyB,SAAS,CAACE,YAAY,CAAC;QACzDtC,QAAQ,EAAEA,CAAA,KAAMU,iBAAiB,CAAC0B,SAAS,CAACE,YAAY;MAC1D,CAAC;MACD3E,QAAQ,EAAEmD,mBAAmB,KAAKsB,SAAS,CAACE;IAC9C,CAAC,CAAC,CAAC;IACHnB,QAAQ,CAACe,SAAS,CAAC;EACrB,CAAC,EAAE,CAAC1B,UAAU,EAAEM,mBAAmB,EAAEH,iBAAiB,EAAED,iBAAiB,EAAES,QAAQ,CAAC,CAAC;;EAErF;EACAzE,SAAS,CAAC,MAAM;IACd,MAAM+F,SAAS,GAAGhC,WAAW,CAAC0B,GAAG,CAAC,CAACO,UAAU,EAAEf,KAAK,KAAK;MACvD,MAAMgB,eAAe,GAAGnC,UAAU,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKK,UAAU,CAACI,mBAAmB,CAAC;MACrF,MAAMC,eAAe,GAAGvC,UAAU,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKK,UAAU,CAACM,mBAAmB,CAAC;;MAErF;MACA,MAAMC,YAAY,GAAIC,UAAU,IAAK;QACnC,MAAMnF,MAAM,GAAG;UACbC,UAAU,EAAE,SAAS;UACrBC,cAAc,EAAE,SAAS;UACzBC,UAAU,EAAE,SAAS;UACrBE,UAAU,EAAE,SAAS;UACrBD,MAAM,EAAE;QACV,CAAC;QACD,OAAOJ,MAAM,CAACmF,UAAU,CAAC,IAAI,SAAS;MACxC,CAAC;MAED,OAAO;QACLb,EAAE,EAAEK,UAAU,CAACS,aAAa,IAAI,QAAQxB,KAAK,EAAE;QAC/CyB,MAAM,EAAEV,UAAU,CAACI,mBAAmB,CAACO,QAAQ,CAAC,CAAC;QACjDC,MAAM,EAAEZ,UAAU,CAACM,mBAAmB,CAACK,QAAQ,CAAC,CAAC;QACjDvF,IAAI,EAAE,YAAY;QAClByF,QAAQ,EAAE,IAAI;QACd1E,KAAK,EAAE;UACL2E,MAAM,EAAEP,YAAY,CAACN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAElE,cAAc,CAAC;UACrDgF,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE;QACnB,CAAC;QACDC,SAAS,EAAE;UACT7F,IAAI,EAAE,aAAa;UACnB8F,KAAK,EAAEX,YAAY,CAACN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAElE,cAAc;QACrD,CAAC;QACDoF,KAAK,EAAE,GAAG,CAAAlB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAExD,IAAI,KAAI,QAAQ,MAAM,CAAA4D,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE5D,IAAI,KAAI,QAAQ,EAAE;QACpF2E,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,GAAG;UACfvE,IAAI,EAAE;QACR,CAAC;QACDwE,YAAY,EAAE;UACZxE,IAAI,EAAE,SAAS;UACfyE,WAAW,EAAE,GAAG;UAChBC,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE;QACN;MACF,CAAC;IACH,CAAC,CAAC;IACF9C,QAAQ,CAACmB,SAAS,CAAC;EACrB,CAAC,EAAE,CAAChC,WAAW,EAAED,UAAU,EAAEc,QAAQ,CAAC,CAAC;EAEvC,MAAM+C,SAAS,GAAG9H,WAAW,CAC1B+H,MAAM,IAAK;IAAA,IAAAC,qBAAA;IACV;IACA,MAAM5B,eAAe,GAAGnC,UAAU,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACP,YAAY,KAAKgC,MAAM,CAAClB,MAAM,CAAC;IAC9E,MAAML,eAAe,GAAGvC,UAAU,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACP,YAAY,KAAKgC,MAAM,CAAChB,MAAM,CAAC;IAE9E,IAAI,CAACX,eAAe,IAAI,CAACI,eAAe,EAAE;;IAE1C;IACA,IAAIuB,MAAM,CAAClB,MAAM,KAAKkB,MAAM,CAAChB,MAAM,EAAE;MACnCkB,KAAK,CAAC,uCAAuC,CAAC;MAC9C;IACF;;IAEA;IACA,MAAMC,kBAAkB,GAAGhE,WAAW,CAACmC,IAAI,CACzC8B,IAAI,IAAIA,IAAI,CAAC5B,mBAAmB,CAACO,QAAQ,CAAC,CAAC,KAAKiB,MAAM,CAAClB,MAAM,IACrDsB,IAAI,CAAC1B,mBAAmB,CAACK,QAAQ,CAAC,CAAC,KAAKiB,MAAM,CAAChB,MACzD,CAAC;IAED,IAAImB,kBAAkB,EAAE;MACtBD,KAAK,CAAC,4BAA4B,CAAC;MACnC;IACF;;IAEA;IACA,MAAMG,gBAAgB,GAAG;MACvB3G,UAAU,EAAE,CAAC,gBAAgB,EAAE,YAAY,EAAE,YAAY,CAAC;MAC1DC,cAAc,EAAE,CAAC,YAAY,CAAC;MAC9BG,UAAU,EAAE,CAAC,YAAY,CAAC;MAC1BF,UAAU,EAAE,CAAC,QAAQ,CAAC;MACtBC,MAAM,EAAE;IACV,CAAC;IAED,IAAI,GAAAoG,qBAAA,GAACI,gBAAgB,CAAChC,eAAe,CAAClE,cAAc,CAAC,cAAA8F,qBAAA,eAAhDA,qBAAA,CAAkDK,QAAQ,CAAC7B,eAAe,CAACtE,cAAc,CAAC,GAAE;MAC/F+F,KAAK,CAAC,kBAAkB7B,eAAe,CAAClE,cAAc,OAAOsE,eAAe,CAACtE,cAAc,yBAAyB,CAAC;MACrH;IACF;IAEA,MAAMoG,aAAa,GAAG;MACpB1B,aAAa,EAAE,QAAQ2B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACnCjC,mBAAmB,EAAEkC,QAAQ,CAACV,MAAM,CAAClB,MAAM,CAAC;MAC5CJ,mBAAmB,EAAEgC,QAAQ,CAACV,MAAM,CAAChB,MAAM;IAC7C,CAAC;IAEDzC,kBAAkB,CAACgE,aAAa,CAAC;EACnC,CAAC,EACD,CAAChE,kBAAkB,EAAEL,UAAU,EAAEC,WAAW,CAC9C,CAAC;EAED,MAAMwE,UAAU,GAAG1I,WAAW,CAAE2I,KAAK,IAAK;IACxCA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,YAAY,CAACC,UAAU,GAAG,MAAM;EACxC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,MAAM,GAAG/I,WAAW,CACvB2I,KAAK,IAAK;IAAA,IAAAK,qBAAA;IACTL,KAAK,CAACC,cAAc,CAAC,CAAC;IAEtB,MAAMK,eAAe,GAAGvE,gBAAgB,CAACwE,OAAO,CAACC,qBAAqB,CAAC,CAAC;IACxE,MAAMC,aAAa,GAAGT,KAAK,CAACE,YAAY,CAACQ,OAAO,CAAC,uBAAuB,CAAC;IAEzE,IAAI,OAAOD,aAAa,KAAK,WAAW,IAAI,CAACA,aAAa,EAAE;MAC1D;IACF;IAEA,MAAMhH,QAAQ,GAAG6C,iBAAiB,CAACqE,OAAO,CAAC;MACzChE,CAAC,EAAEqD,KAAK,CAACY,OAAO,GAAGN,eAAe,CAACO,IAAI;MACvCjE,CAAC,EAAEoD,KAAK,CAACc,OAAO,GAAGR,eAAe,CAAC1G;IACrC,CAAC,CAAC;;IAEF;IACA,MAAMmH,gBAAgB,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC;IACjD,IAAIA,gBAAgB,CAACrB,QAAQ,CAACe,aAAa,CAAC,EAAE;MAC5C,MAAMO,iBAAiB,GAAG1F,UAAU,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpE,cAAc,KAAKkH,aAAa,CAAC;MAClF,IAAIO,iBAAiB,EAAE;QACrB1B,KAAK,CAAC,YAAYmB,aAAa,CAACvG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,qCAAqC,CAAC;QACvF;MACF;IACF;IAEA,MAAM+G,YAAY,GAAG;MACnB7D,YAAY,EAAE,GAAGqD,aAAa,IAAIb,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC9CtG,cAAc,EAAEkH,aAAa;MAC7BxG,IAAI,EAAE,EAAAoG,qBAAA,GAAAjI,iBAAiB,CAACqI,aAAa,CAAC,cAAAJ,qBAAA,uBAAhCA,qBAAA,CAAkCpG,IAAI,KAAIwG,aAAa;MAC7DpD,UAAU,EAAE5D,QAAQ,CAACkD,CAAC;MACtBW,UAAU,EAAE7D,QAAQ,CAACmD,CAAC;MACtBvC,aAAa,EAAE,CAAC;IAClB,CAAC;IAEDqB,cAAc,CAACuF,YAAY,CAAC;EAC9B,CAAC,EACD,CAAC3E,iBAAiB,EAAEZ,cAAc,EAAEJ,UAAU,CAChD,CAAC;EAED,MAAM4F,WAAW,GAAG7J,WAAW,CAC7B,CAAC2I,KAAK,EAAEmB,IAAI,KAAK;IACf3F,iBAAiB,CAAC2F,IAAI,CAAChE,EAAE,CAAC;EAC5B,CAAC,EACD,CAAC3B,iBAAiB,CACpB,CAAC;EAED,MAAM4F,cAAc,GAAG/J,WAAW,CAChC,CAAC2I,KAAK,EAAEmB,IAAI,KAAK;IACf;IACA,MAAMjE,SAAS,GAAG5B,UAAU,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACP,YAAY,KAAK+D,IAAI,CAAChE,EAAE,CAAC;IAClE,IAAID,SAAS,EAAE;MACbmE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEH,IAAI,CAAChE,EAAE,EAAEgE,IAAI,CAAC1H,QAAQ,CAAC;MAClD;IACF;EACF,CAAC,EACD,CAAC6B,UAAU,CACb,CAAC;EAED,MAAMiG,mBAAmB,GAAG;IAC1BhD,WAAW,EAAE,CAAC;IACdD,MAAM,EAAE,SAAS;IACjBE,eAAe,EAAE;EACnB,CAAC;EAED,MAAMgD,kBAAkB,GAAG;IACzB7H,KAAK,EAAE;MAAE4E,WAAW,EAAE,CAAC;MAAED,MAAM,EAAE;IAAU,CAAC;IAC5C1F,IAAI,EAAE,YAAY;IAClB6F,SAAS,EAAE;MACT7F,IAAI,EAAE,aAAa;MACnB8F,KAAK,EAAE;IACT;EACF,CAAC;EAED,oBACEpG,OAAA;IAAKgB,SAAS,EAAC,0BAA0B;IAACmI,GAAG,EAAE1F,gBAAiB;IAAAvC,QAAA,eAC9DlB,OAAA,CAACb,SAAS;MACRuE,KAAK,EAAEA,KAAM;MACbG,KAAK,EAAEA,KAAM;MACbD,aAAa,EAAEA,aAAc;MAC7BG,aAAa,EAAEA,aAAc;MAC7B8C,SAAS,EAAEA,SAAU;MACrBuC,MAAM,EAAEnF,oBAAqB;MAC7B6D,MAAM,EAAEA,MAAO;MACfL,UAAU,EAAEA,UAAW;MACvBmB,WAAW,EAAEA,WAAY;MACzBE,cAAc,EAAEA,cAAe;MAC/BjG,SAAS,EAAEA,SAAU;MACrBwG,kBAAkB,EAAE3J,kBAAkB,CAAC4J,UAAW;MAClDL,mBAAmB,EAAEA,mBAAoB;MACzCC,kBAAkB,EAAEA,kBAAmB;MACvCK,OAAO;MACPC,mBAAmB,EAAC,aAAa;MACjCC,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAK,CAAE;MACtC1I,SAAS,EAAC,YAAY;MAAAE,QAAA,gBAEtBlB,OAAA,CAACZ,OAAO;QACNuK,eAAe,EAAGC,CAAC,IAAK;UAAA,IAAAC,WAAA;UACtB,MAAMC,QAAQ,IAAAD,WAAA,GAAGnG,KAAK,CAAC0B,IAAI,CAACyD,IAAI,IAAIA,IAAI,CAAChE,EAAE,KAAK+E,CAAC,CAAC/E,EAAE,CAAC,cAAAgF,WAAA,uBAApCA,WAAA,CAAsC3J,IAAI;UAC3D,IAAI,CAAA4J,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,YAAY,EAAE,OAAO,SAAS;UAC/D,IAAI,CAAA6I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,gBAAgB,EAAE,OAAO,SAAS;UACnE,IAAI,CAAA6I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,YAAY,EAAE,OAAO,SAAS;UAC/D,IAAI,CAAA6I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,QAAQ,EAAE,OAAO,SAAS;UAC3D,IAAI,CAAA6I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,YAAY,EAAE,OAAO,SAAS;UAC/D,OAAO,SAAS;QAClB,CAAE;QACF8I,SAAS,EAAGH,CAAC,IAAK;UAAA,IAAAI,YAAA;UAChB,MAAMF,QAAQ,IAAAE,YAAA,GAAGtG,KAAK,CAAC0B,IAAI,CAACyD,IAAI,IAAIA,IAAI,CAAChE,EAAE,KAAK+E,CAAC,CAAC/E,EAAE,CAAC,cAAAmF,YAAA,uBAApCA,YAAA,CAAsC9J,IAAI;UAC3D,IAAI,CAAA4J,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,YAAY,EAAE,OAAO,SAAS;UAC/D,IAAI,CAAA6I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,gBAAgB,EAAE,OAAO,SAAS;UACnE,IAAI,CAAA6I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,YAAY,EAAE,OAAO,SAAS;UAC/D,IAAI,CAAA6I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,QAAQ,EAAE,OAAO,SAAS;UAC3D,IAAI,CAAA6I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE7I,cAAc,MAAK,YAAY,EAAE,OAAO,SAAS;UAC/D,OAAO,SAAS;QAClB,CAAE;QACFgJ,gBAAgB,EAAE,CAAE;QACpBjJ,SAAS,EAAC;MAA4C;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACF1B,OAAA,CAACX,QAAQ;QACP2B,SAAS,EAAC,sDAAsD;QAChEkJ,QAAQ,EAAE,IAAK;QACfC,WAAW,EAAE,IAAK;QAClBC,eAAe,EAAE;MAAK;QAAA7I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACF1B,OAAA,CAACV,UAAU;QACT8G,KAAK,EAAC,SAAS;QACfiE,GAAG,EAAE,EAAG;QACRC,IAAI,EAAE,CAAE;QACRC,OAAO,EAAC;MAAM;QAAAhJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAEF1B,OAAA,CAACL,KAAK;QAACwB,QAAQ,EAAC,UAAU;QAACH,SAAS,EAAC,8DAA8D;QAAAE,QAAA,gBACjGlB,OAAA;UAAKgB,SAAS,EAAC,6BAA6B;UAAAE,QAAA,gBAC1ClB,OAAA;YAAKgB,SAAS,EAAC,kGAAkG;YAAAE,QAAA,eAC/GlB,OAAA;cAAMgB,SAAS,EAAC,8BAA8B;cAAAE,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN1B,OAAA;YAAAkB,QAAA,gBACElB,OAAA;cAAIgB,SAAS,EAAC,iCAAiC;cAAAE,QAAA,EAC5C,CAAAqC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE5B,IAAI,KAAI;YAAqB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACL1B,OAAA;cAAKgB,SAAS,EAAC,uBAAuB;cAAAE,QAAA,GACnC8B,UAAU,CAAChB,MAAM,EAAC,qBAAc,EAACiB,WAAW,CAACjB,MAAM,EAAC,cACvD;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1B,OAAA;UAAKgB,SAAS,EAAC,oCAAoC;UAAAE,QAAA,eACjDlB,OAAA;YAAKgB,SAAS,EAAC,6BAA6B;YAAAE,QAAA,EACzC8B,UAAU,CAACwH,IAAI,CAACnF,CAAC,IAAIA,CAAC,CAACpE,cAAc,KAAK,YAAY,CAAC,IACvD+B,UAAU,CAACwH,IAAI,CAACnF,CAAC,IAAIA,CAAC,CAACpE,cAAc,KAAK,QAAQ,CAAC,gBAClDjB,OAAA;cAAKgB,SAAS,EAAC,4CAA4C;cAAAE,QAAA,gBACzDlB,OAAA;gBAAKgB,SAAS,EAAC,SAAS;gBAACiB,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAhB,QAAA,eAC9DlB,OAAA;kBAAMmC,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,oHAAoH;kBAACC,QAAQ,EAAC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClK,CAAC,eACN1B,OAAA;gBAAMgB,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,gBAEN1B,OAAA;cAAKgB,SAAS,EAAC,4CAA4C;cAAAE,QAAA,gBACzDlB,OAAA;gBAAKgB,SAAS,EAAC,SAAS;gBAACiB,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAhB,QAAA,eAC9DlB,OAAA;kBAAMmC,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,mNAAmN;kBAACC,QAAQ,EAAC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjQ,CAAC,eACN1B,OAAA;gBAAMgB,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEPsB,UAAU,CAAChB,MAAM,KAAK,CAAC,iBACtBhC,OAAA,CAACL,KAAK;QAACwB,QAAQ,EAAC,QAAQ;QAACH,SAAS,EAAC,qBAAqB;QAAAE,QAAA,eACtDlB,OAAA;UAAKgB,SAAS,EAAC,gFAAgF;UAAAE,QAAA,gBAC7FlB,OAAA;YAAKgB,SAAS,EAAC,mHAAmH;YAAAE,QAAA,eAChIlB,OAAA;cAAMgB,SAAS,EAAC,UAAU;cAAAE,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACN1B,OAAA;YAAIgB,SAAS,EAAC,sCAAsC;YAAAE,QAAA,EAAC;UAErD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1B,OAAA;YAAGgB,SAAS,EAAC,oCAAoC;YAAAE,QAAA,EAAC;UAElD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ1B,OAAA;YAAKgB,SAAS,EAAC,2BAA2B;YAAAE,QAAA,gBACxClB,OAAA;cAAKgB,SAAS,EAAC,wCAAwC;cAAAE,QAAA,EAAC;YAExD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN1B,OAAA;cAAKgB,SAAS,EAAC,2CAA2C;cAAAE,QAAA,gBACxDlB,OAAA;gBAAAkB,QAAA,EAAK;cAA6B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxC1B,OAAA;gBAAAkB,QAAA,EAAK;cAAmC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C1B,OAAA;gBAAAkB,QAAA,EAAK;cAA8B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzC1B,OAAA;gBAAAkB,QAAA,EAAK;cAA0B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrC1B,OAAA;gBAAAkB,QAAA,EAAK;cAAwB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC8B,EAAA,CAlWIT,kBAAkB;EAAA,QAWmBxD,aAAa,EACbC,aAAa;AAAA;AAAAiL,GAAA,GAZlD1H,kBAAkB;AAoWxB,eAAeA,kBAAkB;AAAC,IAAAH,EAAA,EAAA6H,GAAA;AAAAC,YAAA,CAAA9H,EAAA;AAAA8H,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}