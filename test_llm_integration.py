#!/usr/bin/env python3
"""
Test LLM integration with improved prompts
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_improved_llm():
    """Test the improved LLM integration"""
    print("🔍 Testing improved LLM integration...")
    
    # Create a workflow with better custom prompt
    workflow_data = {
        "name": "Improved LLM Test Workflow",
        "description": "Testing improved LLM integration with better prompts"
    }
    response = requests.post(f"{BASE_URL}/api/workflows/", json=workflow_data)
    if response.status_code != 200:
        print(f"❌ Failed to create workflow: {response.text}")
        return False
    
    workflow = response.json()
    workflow_id = workflow["id"]
    print(f"✅ Created workflow ID: {workflow_id}")
    
    # Add User Query component
    user_query_data = {
        "component_type": "user_query",
        "component_id": "user_query_1",
        "name": "User Query",
        "position_x": 100,
        "position_y": 100,
        "configuration": {}
    }
    response = requests.post(f"{BASE_URL}/api/workflows/{workflow_id}/components", json=user_query_data)
    if response.status_code != 200:
        print(f"❌ Failed to add User Query: {response.text}")
        return False
    
    # Add Knowledge Base component
    kb_data = {
        "component_type": "knowledge_base",
        "component_id": "knowledge_base_1",
        "name": "Knowledge Base",
        "position_x": 400,
        "position_y": 100,
        "configuration": {
            "top_k": 3,
            "similarity_threshold": 0.6
        }
    }
    response = requests.post(f"{BASE_URL}/api/workflows/{workflow_id}/components", json=kb_data)
    if response.status_code != 200:
        print(f"❌ Failed to add Knowledge Base: {response.text}")
        return False
    
    # Add LLM Engine with improved custom prompt
    llm_data = {
        "component_type": "llm_engine",
        "component_id": "llm_engine_1",
        "name": "LLM Engine",
        "position_x": 700,
        "position_y": 100,
        "configuration": {
            "model": "gpt-3.5-turbo",
            "max_tokens": 500,
            "temperature": 0.7,
            "custom_prompt": """You are an expert assistant for the No-Code/Low-Code Workflow Builder application. 

Context Information:
{context}

User Question: {query}

Please provide a detailed, accurate, and helpful answer based on the context provided. If the context contains relevant information, use it to give specific details. If the context doesn't contain enough information to fully answer the question, acknowledge this and provide what information you can.""",
            "use_web_search": False
        }
    }
    response = requests.post(f"{BASE_URL}/api/workflows/{workflow_id}/components", json=llm_data)
    if response.status_code != 200:
        print(f"❌ Failed to add LLM Engine: {response.text}")
        return False
    
    # Add Output component
    output_data = {
        "component_type": "output",
        "component_id": "output_1",
        "name": "Output",
        "position_x": 1000,
        "position_y": 100,
        "configuration": {}
    }
    response = requests.post(f"{BASE_URL}/api/workflows/{workflow_id}/components", json=output_data)
    if response.status_code != 200:
        print(f"❌ Failed to add Output: {response.text}")
        return False
    
    print("✅ All components added successfully!")
    
    # Test with specific questions
    test_questions = [
        "What is the No-Code/Low-Code Workflow Builder?",
        "What are the four main components available?",
        "How does the Knowledge Base component work with vector embeddings?",
        "What AI models are supported by the LLM Engine component?",
        "How do you deploy this application using Docker?"
    ]
    
    for question in test_questions:
        print(f"\n💬 Testing: '{question}'")
        chat_data = {
            "workflow_id": workflow_id,
            "message": question
        }
        response = requests.post(f"{BASE_URL}/api/chat/", json=chat_data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"⏱️  Execution time: {result.get('execution_time', 0):.2f}s")
            print(f"📝 Response: {result.get('response', 'No response')}")
            
            component_results = result.get('component_results', {})
            if 'knowledge_base' in component_results:
                kb_results = component_results['knowledge_base']
                print(f"📚 Knowledge Base: Found {kb_results.get('results_found', 0)} results, {kb_results.get('context_length', 0)} chars")
            
            if 'llm_engine' in component_results:
                llm_results = component_results['llm_engine']
                print(f"🤖 LLM: Model {llm_results.get('model', 'unknown')}, {llm_results.get('tokens_used', 0)} tokens")
        else:
            print(f"❌ Chat failed: {response.text}")
        
        time.sleep(1)  # Rate limiting
    
    print("\n✅ Improved LLM integration test completed!")
    return True

if __name__ == "__main__":
    test_improved_llm()
