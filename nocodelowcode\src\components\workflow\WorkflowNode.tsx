import React from 'react';
import { <PERSON><PERSON>, <PERSON>sition, NodeProps } from 'reactflow';
import { 
  MessageCircleIcon, 
  DatabaseIcon, 
  BrainIcon, 
  MonitorIcon,
  SettingsIcon 
} from 'lucide-react';

interface WorkflowNodeData {
  label: string;
  componentType: string;
  configuration: Record<string, any>;
  dbId: number;
  isSelected: boolean;
}

const getComponentIcon = (componentType: string) => {
  switch (componentType) {
    case 'user_query':
      return <MessageCircleIcon className="w-5 h-5" />;
    case 'knowledge_base':
      return <DatabaseIcon className="w-5 h-5" />;
    case 'llm_engine':
      return <BrainIcon className="w-5 h-5" />;
    case 'output':
      return <MonitorIcon className="w-5 h-5" />;
    default:
      return <SettingsIcon className="w-5 h-5" />;
  }
};

const getComponentColor = (componentType: string) => {
  switch (componentType) {
    case 'user_query':
      return {
        bg: 'bg-blue-500',
        border: 'border-blue-500',
        text: 'text-blue-700',
        bgLight: 'bg-blue-50',
      };
    case 'knowledge_base':
      return {
        bg: 'bg-green-500',
        border: 'border-green-500',
        text: 'text-green-700',
        bgLight: 'bg-green-50',
      };
    case 'llm_engine':
      return {
        bg: 'bg-purple-500',
        border: 'border-purple-500',
        text: 'text-purple-700',
        bgLight: 'bg-purple-50',
      };
    case 'output':
      return {
        bg: 'bg-orange-500',
        border: 'border-orange-500',
        text: 'text-orange-700',
        bgLight: 'bg-orange-50',
      };
    default:
      return {
        bg: 'bg-gray-500',
        border: 'border-gray-500',
        text: 'text-gray-700',
        bgLight: 'bg-gray-50',
      };
  }
};

const getComponentDescription = (componentType: string) => {
  switch (componentType) {
    case 'user_query':
      return 'Entry point for user input';
    case 'knowledge_base':
      return 'Document search and retrieval';
    case 'llm_engine':
      return 'AI language model processing';
    case 'output':
      return 'Final response display';
    default:
      return 'Workflow component';
  }
};

const WorkflowNode: React.FC<NodeProps<WorkflowNodeData>> = ({ data, selected }) => {
  const colors = getComponentColor(data.componentType);
  const icon = getComponentIcon(data.componentType);
  const description = getComponentDescription(data.componentType);
  
  const isSelected = selected || data.isSelected;

  return (
    <div
      className={`relative bg-white rounded-lg shadow-md border-2 transition-all duration-200 ${
        isSelected 
          ? `${colors.border} shadow-lg` 
          : 'border-gray-200 hover:border-gray-300'
      }`}
      style={{ minWidth: 200, minHeight: 80 }}
    >
      {/* Selection indicator */}
      {isSelected && (
        <div className={`absolute -inset-1 ${colors.bg} rounded-lg opacity-20`} />
      )}

      {/* Header */}
      <div className={`${colors.bgLight} px-4 py-2 rounded-t-lg border-b border-gray-100`}>
        <div className="flex items-center space-x-2">
          <div className={`${colors.bg} text-white p-1.5 rounded`}>
            {icon}
          </div>
          <div className="flex-1">
            <h3 className={`text-sm font-semibold ${colors.text}`}>
              {data.label}
            </h3>
          </div>
          {Object.keys(data.configuration).length > 0 && (
            <div className="w-2 h-2 bg-green-400 rounded-full" title="Configured" />
          )}
        </div>
      </div>

      {/* Body */}
      <div className="px-4 py-3">
        <p className="text-xs text-gray-600">
          {description}
        </p>
        
        {/* Configuration summary */}
        {Object.keys(data.configuration).length > 0 && (
          <div className="mt-2 text-xs text-gray-500">
            <span className="inline-flex items-center px-2 py-1 rounded-full bg-gray-100">
              <SettingsIcon className="w-3 h-3 mr-1" />
              Configured
            </span>
          </div>
        )}
      </div>

      {/* Handles for connections */}
      {data.componentType !== 'user_query' && (
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 !bg-gray-400 !border-2 !border-white"
        />
      )}
      
      {data.componentType !== 'output' && (
        <Handle
          type="source"
          position={Position.Bottom}
          className="w-3 h-3 !bg-gray-400 !border-2 !border-white"
        />
      )}

      {/* Status indicator */}
      <div className="absolute top-2 right-2">
        <div className="w-2 h-2 bg-green-400 rounded-full" title="Active" />
      </div>
    </div>
  );
};

export default WorkflowNode;
