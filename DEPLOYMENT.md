# Deployment Guide

## Quick Start (Recommended)

### Windows
```bash
# Run the startup script
start.bat
```

### Linux/Mac
```bash
# Make the script executable
chmod +x start.sh

# Run the startup script
./start.sh
```

## Manual Deployment

### Prerequisites
- Docker and Docker Compose
- OpenAI API Key (already configured)

### Step 1: Clone and Setup
```bash
git clone <repository-url>
cd noCodeLowCode
```

### Step 2: Environment Configuration
The OpenAI API key is already configured in the environment files:
- `backend/.env` - Backend configuration
- `nocodelowcode/.env` - Frontend configuration

### Step 3: Start Services
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Step 4: Access Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **PostgreSQL**: localhost:5432
- **ChromaDB**: localhost:8001

## Service Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   PostgreSQL    │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (Database)    │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │    ChromaDB     │    │     Redis       │
                       │  (Vector Store) │    │   (Caching)     │
                       │   Port: 8001    │    │   Port: 6379    │
                       └─────────────────┘    └─────────────────┘
```

## Testing the Deployment

### Automated Test
```bash
python test_backend.py
```

### Manual Testing
1. **Health Check**: Visit http://localhost:8000/health
2. **API Docs**: Visit http://localhost:8000/docs
3. **Frontend**: Visit http://localhost:3000
4. **Create Workflow**: Use the UI to create a new workflow
5. **Add Components**: Drag components to the workspace
6. **Test Chat**: Use "Chat with Stack" to test the workflow

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000

# Kill the process or change ports in docker-compose.yml
```

#### 2. Database Connection Issues
```bash
# Check PostgreSQL logs
docker-compose logs postgres

# Reset database
docker-compose down -v
docker-compose up -d
```

#### 3. ChromaDB Issues
```bash
# Check ChromaDB logs
docker-compose logs chromadb

# Reset ChromaDB data
docker-compose down
docker volume rm nocodelowcode_chroma_data
docker-compose up -d
```

#### 4. Frontend Build Issues
```bash
# Rebuild frontend
docker-compose build frontend
docker-compose up -d frontend
```

#### 5. Backend API Issues
```bash
# Check backend logs
docker-compose logs backend

# Rebuild backend
docker-compose build backend
docker-compose up -d backend
```

### Performance Optimization

#### 1. Resource Allocation
```yaml
# In docker-compose.yml, add resource limits
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

#### 2. Database Optimization
```bash
# Increase PostgreSQL shared_buffers
# Add to docker-compose.yml postgres service:
command: postgres -c shared_buffers=256MB -c max_connections=200
```

#### 3. Scaling Services
```bash
# Scale backend instances
docker-compose up -d --scale backend=3

# Use nginx for load balancing (add nginx service to docker-compose.yml)
```

## Production Deployment

### Environment Variables
Update the following for production:
```bash
# Backend (.env)
SECRET_KEY=your-production-secret-key
DATABASE_URL=***********************************/dbname
ALLOWED_ORIGINS=["https://yourdomain.com"]

# Frontend (.env)
REACT_APP_API_URL=https://api.yourdomain.com
```

### Security Considerations
1. **HTTPS**: Use SSL certificates
2. **Database**: Use managed database service
3. **API Keys**: Use environment variables or secret management
4. **CORS**: Restrict allowed origins
5. **Authentication**: Implement user authentication

### Monitoring
```bash
# Add monitoring services to docker-compose.yml
# - Prometheus for metrics
# - Grafana for dashboards
# - ELK stack for logging
```

## Kubernetes Deployment (Optional)

### Prerequisites
- Kubernetes cluster
- kubectl configured

### Deploy to Kubernetes
```bash
# Create namespace
kubectl create namespace nocodelowcode

# Apply manifests
kubectl apply -f k8s/ -n nocodelowcode

# Check status
kubectl get pods -n nocodelowcode
```

### Kubernetes Services
```yaml
# Example service manifest
apiVersion: v1
kind: Service
metadata:
  name: backend-service
spec:
  selector:
    app: backend
  ports:
    - port: 8000
      targetPort: 8000
  type: LoadBalancer
```

## Backup and Recovery

### Database Backup
```bash
# Backup PostgreSQL
docker-compose exec postgres pg_dump -U postgres nocodelowcode > backup.sql

# Restore PostgreSQL
docker-compose exec -T postgres psql -U postgres nocodelowcode < backup.sql
```

### Vector Store Backup
```bash
# Backup ChromaDB data
docker cp $(docker-compose ps -q chromadb):/chroma/chroma ./chroma_backup

# Restore ChromaDB data
docker cp ./chroma_backup $(docker-compose ps -q chromadb):/chroma/chroma
```

## Maintenance

### Updates
```bash
# Pull latest images
docker-compose pull

# Restart services
docker-compose down
docker-compose up -d
```

### Cleanup
```bash
# Remove unused containers and images
docker system prune -a

# Remove unused volumes
docker volume prune
```

### Logs Management
```bash
# Rotate logs
docker-compose logs --tail=1000 > app.log

# Clear logs
docker-compose down
docker system prune -a
docker-compose up -d
```

## Support

For deployment issues:
1. Check the logs: `docker-compose logs -f`
2. Verify all services are running: `docker-compose ps`
3. Test connectivity: `python test_backend.py`
4. Review the troubleshooting section above
