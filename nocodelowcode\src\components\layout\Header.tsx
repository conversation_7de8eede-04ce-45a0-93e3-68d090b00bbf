import React, { useState } from 'react';
import { Workflow } from '../../types';
import { ChevronDownIcon, PlusIcon, PlayIcon, MessageSquareIcon } from 'lucide-react';

interface HeaderProps {
  workflows: Workflow[];
  currentWorkflow?: Workflow;
  onWorkflowSelect: (workflow: Workflow) => void;
  onWorkflowCreate: () => void;
  onBuildStack: () => void;
  onChatWithStack: () => void;
}

const Header: React.FC<HeaderProps> = ({
  workflows,
  currentWorkflow,
  onWorkflowSelect,
  onWorkflowCreate,
  onBuildStack,
  onChatWithStack,
}) => {
  const [isWorkflowDropdownOpen, setIsWorkflowDropdownOpen] = useState(false);

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Logo and Workflow selector */}
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <svg
                className="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <h1 className="text-xl font-bold text-gray-900">
              No-Code Workflow Builder
            </h1>
          </div>

          {/* Workflow Selector */}
          <div className="relative">
            <button
              onClick={() => setIsWorkflowDropdownOpen(!isWorkflowDropdownOpen)}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <span className="text-sm font-medium text-gray-700">
                {currentWorkflow ? currentWorkflow.name : 'Select Workflow'}
              </span>
              <ChevronDownIcon className="w-4 h-4 text-gray-500" />
            </button>

            {isWorkflowDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <div className="py-1">
                  <button
                    onClick={() => {
                      onWorkflowCreate();
                      setIsWorkflowDropdownOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Create New Workflow
                  </button>
                  
                  {workflows.length > 0 && (
                    <>
                      <div className="border-t border-gray-100 my-1" />
                      {workflows.map((workflow) => (
                        <button
                          key={workflow.id}
                          onClick={() => {
                            onWorkflowSelect(workflow);
                            setIsWorkflowDropdownOpen(false);
                          }}
                          className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 ${
                            currentWorkflow?.id === workflow.id
                              ? 'bg-primary-50 text-primary-700'
                              : 'text-gray-700'
                          }`}
                        >
                          <div className="flex-1 text-left">
                            <div className="font-medium">{workflow.name}</div>
                            {workflow.description && (
                              <div className="text-xs text-gray-500 truncate">
                                {workflow.description}
                              </div>
                            )}
                          </div>
                          {workflow.is_active && (
                            <div className="w-2 h-2 bg-green-400 rounded-full ml-2" />
                          )}
                        </button>
                      ))}
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Action buttons */}
        <div className="flex items-center space-x-3">
          {currentWorkflow && (
            <>
              <button
                onClick={onBuildStack}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlayIcon className="w-4 h-4 mr-2" />
                Build Stack
              </button>

              <button
                onClick={onChatWithStack}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <MessageSquareIcon className="w-4 h-4 mr-2" />
                Chat with Stack
              </button>
            </>
          )}

          {/* User menu placeholder */}
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <svg
              className="w-5 h-5 text-gray-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {isWorkflowDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsWorkflowDropdownOpen(false)}
        />
      )}
    </header>
  );
};

export default Header;
