@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* React Flow Styles */
.react-flow__node {
  font-family: 'Inter', sans-serif;
}

.react-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 2;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #1d4ed8;
  stroke-width: 3;
}

.react-flow__handle {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border: 2px solid #ffffff;
}

.react-flow__handle.react-flow__handle-top {
  top: -4px;
}

.react-flow__handle.react-flow__handle-bottom {
  bottom: -4px;
}

.react-flow__handle.react-flow__handle-left {
  left: -4px;
}

.react-flow__handle.react-flow__handle-right {
  right: -4px;
}
