import React from 'react';
import { ConnectionLineType, ConnectionMode, PanOnScrollMode, SelectionMode } from '../../types';
import type { EdgeTypes, NodeOrigin, NodeTypes, Viewport } from '../../types';
declare const ReactFlow: React.ForwardRefExoticComponent<Omit<React.HTMLAttributes<HTMLDivElement>, "onError"> & {
    nodes?: import("../../types").Node<any, string | undefined>[] | undefined;
    edges?: import("../../types").Edge<any>[] | undefined;
    defaultNodes?: import("../../types").Node<any, string | undefined>[] | undefined;
    defaultEdges?: import("../../types").Edge<any>[] | undefined;
    defaultEdgeOptions?: import("../../types").DefaultEdgeOptions | undefined;
    onNodeClick?: import("../../types").NodeMouseHandler | undefined;
    onNodeDoubleClick?: import("../../types").NodeMouseHandler | undefined;
    onNodeMouseEnter?: import("../../types").NodeMouseHandler | undefined;
    onNodeMouseMove?: import("../../types").NodeMouseHandler | undefined;
    onNodeMouseLeave?: import("../../types").NodeMouseHandler | undefined;
    onNodeContextMenu?: import("../../types").NodeMouseHandler | undefined;
    onNodeDragStart?: import("../../types").NodeDragHandler | undefined;
    onNodeDrag?: import("../../types").NodeDragHandler | undefined;
    onNodeDragStop?: import("../../types").NodeDragHandler | undefined;
    onEdgeClick?: ((event: React.MouseEvent<Element, MouseEvent>, node: import("../../types").Edge<any>) => void) | undefined;
    onEdgeUpdate?: import("../../types").OnEdgeUpdateFunc<any> | undefined;
    onEdgeUpdateStart?: ((event: React.MouseEvent<Element, MouseEvent>, edge: import("../../types").Edge<any>, handleType: import("../../types").HandleType) => void) | undefined;
    onEdgeUpdateEnd?: ((event: MouseEvent | TouchEvent, edge: import("../../types").Edge<any>, handleType: import("../../types").HandleType) => void) | undefined;
    onReconnect?: import("../../types").OnEdgeUpdateFunc<any> | undefined;
    onReconnectStart?: ((event: React.MouseEvent<Element, MouseEvent>, edge: import("../../types").Edge<any>, handleType: import("../../types").HandleType) => void) | undefined;
    onReconnectEnd?: ((event: MouseEvent | TouchEvent, edge: import("../../types").Edge<any>, handleType: import("../../types").HandleType) => void) | undefined;
    onEdgeContextMenu?: import("../../types").EdgeMouseHandler | undefined;
    onEdgeMouseEnter?: import("../../types").EdgeMouseHandler | undefined;
    onEdgeMouseMove?: import("../../types").EdgeMouseHandler | undefined;
    onEdgeMouseLeave?: import("../../types").EdgeMouseHandler | undefined;
    onEdgeDoubleClick?: import("../../types").EdgeMouseHandler | undefined;
    onNodesChange?: import("../../types").OnNodesChange | undefined;
    onEdgesChange?: import("../../types").OnEdgesChange | undefined;
    onNodesDelete?: import("../../types").OnNodesDelete | undefined;
    onEdgesDelete?: import("../../types").OnEdgesDelete | undefined;
    onSelectionDragStart?: import("../../types").SelectionDragHandler | undefined;
    onSelectionDrag?: import("../../types").SelectionDragHandler | undefined;
    onSelectionDragStop?: import("../../types").SelectionDragHandler | undefined;
    onSelectionStart?: ((event: React.MouseEvent<Element, MouseEvent>) => void) | undefined;
    onSelectionEnd?: ((event: React.MouseEvent<Element, MouseEvent>) => void) | undefined;
    onSelectionContextMenu?: ((event: React.MouseEvent<Element, MouseEvent>, nodes: import("../../types").Node<any, string | undefined>[]) => void) | undefined;
    onConnect?: import("../../types").OnConnect | undefined;
    onConnectStart?: import("../../types").OnConnectStart | undefined;
    onConnectEnd?: import("../../types").OnConnectEnd | undefined;
    onClickConnectStart?: import("../../types").OnConnectStart | undefined;
    onClickConnectEnd?: import("../../types").OnConnectEnd | undefined;
    onInit?: import("../../types").OnInit<any, any> | undefined;
    onMove?: import("../../types").OnMove | undefined;
    onMoveStart?: import("../../types").OnMove | undefined;
    onMoveEnd?: import("../../types").OnMove | undefined;
    onSelectionChange?: import("../../types").OnSelectionChangeFunc | undefined;
    onPaneScroll?: ((event?: React.WheelEvent<Element> | undefined) => void) | undefined;
    onPaneClick?: ((event: React.MouseEvent<Element, MouseEvent>) => void) | undefined;
    onPaneContextMenu?: ((event: React.MouseEvent<Element, MouseEvent>) => void) | undefined;
    onPaneMouseEnter?: ((event: React.MouseEvent<Element, MouseEvent>) => void) | undefined;
    onPaneMouseMove?: ((event: React.MouseEvent<Element, MouseEvent>) => void) | undefined;
    onPaneMouseLeave?: ((event: React.MouseEvent<Element, MouseEvent>) => void) | undefined;
    nodeTypes?: NodeTypes | undefined;
    edgeTypes?: EdgeTypes | undefined;
    connectionLineType?: ConnectionLineType | undefined;
    connectionLineStyle?: React.CSSProperties | undefined;
    connectionLineComponent?: import("../../types").ConnectionLineComponent | undefined;
    connectionLineContainerStyle?: React.CSSProperties | undefined;
    connectionMode?: ConnectionMode | undefined;
    deleteKeyCode?: import("../../types").KeyCode | null | undefined;
    selectionKeyCode?: import("../../types").KeyCode | null | undefined;
    selectionOnDrag?: boolean | undefined;
    selectionMode?: SelectionMode | undefined;
    panActivationKeyCode?: import("../../types").KeyCode | null | undefined;
    multiSelectionKeyCode?: import("../../types").KeyCode | null | undefined;
    zoomActivationKeyCode?: import("../../types").KeyCode | null | undefined;
    snapToGrid?: boolean | undefined;
    snapGrid?: [number, number] | undefined;
    onlyRenderVisibleElements?: boolean | undefined;
    nodesDraggable?: boolean | undefined;
    nodesConnectable?: boolean | undefined;
    nodesFocusable?: boolean | undefined;
    nodeOrigin?: NodeOrigin | undefined;
    edgesFocusable?: boolean | undefined;
    edgesUpdatable?: boolean | undefined;
    initNodeOrigin?: NodeOrigin | undefined;
    elementsSelectable?: boolean | undefined;
    selectNodesOnDrag?: boolean | undefined;
    panOnDrag?: boolean | number[] | undefined;
    minZoom?: number | undefined;
    maxZoom?: number | undefined;
    defaultViewport?: Viewport | undefined;
    translateExtent?: import("../../types").CoordinateExtent | undefined;
    preventScrolling?: boolean | undefined;
    nodeExtent?: import("../../types").CoordinateExtent | undefined;
    defaultMarkerColor?: string | undefined;
    zoomOnScroll?: boolean | undefined;
    zoomOnPinch?: boolean | undefined;
    panOnScroll?: boolean | undefined;
    panOnScrollSpeed?: number | undefined;
    panOnScrollMode?: PanOnScrollMode | undefined;
    zoomOnDoubleClick?: boolean | undefined;
    edgeUpdaterRadius?: number | undefined;
    reconnectRadius?: number | undefined;
    noDragClassName?: string | undefined;
    noWheelClassName?: string | undefined;
    noPanClassName?: string | undefined;
    fitView?: boolean | undefined;
    fitViewOptions?: import("../../types").FitViewOptions | undefined;
    connectOnClick?: boolean | undefined;
    attributionPosition?: import("../../types").PanelPosition | undefined;
    proOptions?: import("../../types").ProOptions | undefined;
    elevateNodesOnSelect?: boolean | undefined;
    elevateEdgesOnSelect?: boolean | undefined;
    disableKeyboardA11y?: boolean | undefined;
    autoPanOnNodeDrag?: boolean | undefined;
    autoPanOnConnect?: boolean | undefined;
    connectionRadius?: number | undefined;
    onError?: import("../../types").OnError | undefined;
    isValidConnection?: import("../../components/Handle/utils").ValidConnectionFunc | undefined;
    nodeDragThreshold?: number | undefined;
} & React.RefAttributes<HTMLDivElement>>;
export default ReactFlow;
//# sourceMappingURL=index.d.ts.map