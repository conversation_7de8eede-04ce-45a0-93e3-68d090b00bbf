{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\noCodeLowCode\\\\nocodelowcode\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [workflows, setWorkflows] = useState([]);\n  const [currentWorkflow, setCurrentWorkflow] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [backendStatus, setBackendStatus] = useState('checking');\n  const [chatMessage, setChatMessage] = useState('');\n  const [chatResponse, setChatResponse] = useState('');\n  const [isChatting, setIsChatting] = useState(false);\n\n  // Check backend health\n  useEffect(() => {\n    fetch('http://localhost:8000/health').then(res => res.json()).then(data => {\n      setBackendStatus('connected');\n      loadWorkflows();\n    }).catch(() => {\n      setBackendStatus('disconnected');\n      setIsLoading(false);\n    });\n  }, []);\n  const loadWorkflows = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/workflows/');\n      const data = await response.json();\n      setWorkflows(data);\n    } catch (error) {\n      console.error('Failed to load workflows:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const createWorkflow = async () => {\n    const name = prompt('Enter workflow name:');\n    if (!name) return;\n    try {\n      const response = await fetch('http://localhost:8000/api/workflows/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          name,\n          description: 'Created from demo'\n        })\n      });\n      const newWorkflow = await response.json();\n      setWorkflows([...workflows, newWorkflow]);\n      setCurrentWorkflow(newWorkflow);\n    } catch (error) {\n      alert('Failed to create workflow');\n    }\n  };\n  const testChat = async () => {\n    if (!currentWorkflow || !chatMessage.trim()) return;\n    setIsChatting(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/chat/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          workflow_id: currentWorkflow.id,\n          message: chatMessage\n        })\n      });\n      const data = await response.json();\n      setChatResponse(data.response);\n    } catch (error) {\n      setChatResponse('Error: Failed to get response');\n    } finally {\n      setIsChatting(false);\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-white\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"No-Code Workflow Builder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `px-2 py-1 rounded-full text-xs font-medium ${backendStatus === 'connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n              children: backendStatus === 'connected' ? '✅ Backend Connected' : '❌ Backend Disconnected'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: createWorkflow,\n              className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\",\n              children: \"Create Workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Workflows\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), workflows.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500 mb-4\",\n              children: \"No workflows created yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: createWorkflow,\n              className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\",\n              children: \"Create Your First Workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: workflows.map(workflow => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => setCurrentWorkflow(workflow),\n              className: `p-4 border rounded-lg cursor-pointer transition-colors ${(currentWorkflow === null || currentWorkflow === void 0 ? void 0 : currentWorkflow.id) === workflow.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium text-gray-900\",\n                children: workflow.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: workflow.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 flex items-center space-x-4 text-xs text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Components: \", workflow.components.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Connections: \", workflow.connections.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 21\n              }, this)]\n            }, workflow.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Test Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), currentWorkflow ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-gray-50 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Testing workflow: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: currentWorkflow.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 74\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Your Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: chatMessage,\n                onChange: e => setChatMessage(e.target.value),\n                placeholder: \"Type your message here...\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                rows: 3\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: testChat,\n              disabled: !chatMessage.trim() || isChatting,\n              className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n              children: isChatting ? 'Processing...' : 'Send Message'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), chatResponse && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-medium text-gray-700 mb-2\",\n                children: \"AI Response:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900\",\n                children: chatResponse\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: \"Select a workflow to test chat functionality\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"System Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-4 bg-green-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mb-2\",\n              children: \"\\uD83C\\uDF10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-green-800\",\n              children: \"Frontend\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-green-600\",\n              children: \"Running on port 3000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-center p-4 rounded-lg ${backendStatus === 'connected' ? 'bg-green-50' : 'bg-red-50'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mb-2\",\n              children: backendStatus === 'connected' ? '🔧' : '❌'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `font-medium ${backendStatus === 'connected' ? 'text-green-800' : 'text-red-800'}`,\n              children: \"Backend API\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-sm ${backendStatus === 'connected' ? 'text-green-600' : 'text-red-600'}`,\n              children: backendStatus === 'connected' ? 'Running on port 8000' : 'Not connected'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-4 bg-blue-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mb-2\",\n              children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-blue-800\",\n              children: \"OpenAI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-600\",\n              children: \"API Key Configured\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"http://localhost:8000/docs\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"text-blue-600 hover:text-blue-800 text-sm\",\n            children: \"\\uD83D\\uDCDA View API Documentation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"IEsQCDFP7eCPdW7vZbpilQdlqZQ=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "App", "_s", "workflows", "setWorkflows", "currentWorkflow", "setCurrentWorkflow", "isLoading", "setIsLoading", "backendStatus", "setBackendStatus", "chatMessage", "setChatMessage", "chatResponse", "setChatResponse", "isChatting", "setIsChatting", "fetch", "then", "res", "json", "data", "loadWorkflows", "catch", "response", "error", "console", "createWorkflow", "name", "prompt", "method", "headers", "body", "JSON", "stringify", "description", "newWorkflow", "alert", "testChat", "trim", "workflow_id", "id", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "length", "map", "workflow", "components", "connections", "value", "onChange", "e", "target", "placeholder", "rows", "disabled", "href", "rel", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/noCodeLowCode/nocodelowcode/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './App.css';\n\nfunction App() {\n  const [workflows, setWorkflows] = useState([]);\n  const [currentWorkflow, setCurrentWorkflow] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [backendStatus, setBackendStatus] = useState('checking');\n  const [chatMessage, setChatMessage] = useState('');\n  const [chatResponse, setChatResponse] = useState('');\n  const [isChatting, setIsChatting] = useState(false);\n\n  // Check backend health\n  useEffect(() => {\n    fetch('http://localhost:8000/health')\n      .then(res => res.json())\n      .then(data => {\n        setBackendStatus('connected');\n        loadWorkflows();\n      })\n      .catch(() => {\n        setBackendStatus('disconnected');\n        setIsLoading(false);\n      });\n  }, []);\n\n  const loadWorkflows = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/workflows/');\n      const data = await response.json();\n      setWorkflows(data);\n    } catch (error) {\n      console.error('Failed to load workflows:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const createWorkflow = async () => {\n    const name = prompt('Enter workflow name:');\n    if (!name) return;\n\n    try {\n      const response = await fetch('http://localhost:8000/api/workflows/', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ name, description: 'Created from demo' })\n      });\n      const newWorkflow = await response.json();\n      setWorkflows([...workflows, newWorkflow]);\n      setCurrentWorkflow(newWorkflow);\n    } catch (error) {\n      alert('Failed to create workflow');\n    }\n  };\n\n  const testChat = async () => {\n    if (!currentWorkflow || !chatMessage.trim()) return;\n\n    setIsChatting(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/chat/', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          workflow_id: currentWorkflow.id,\n          message: chatMessage\n        })\n      });\n      const data = await response.json();\n      setChatResponse(data.response);\n    } catch (error) {\n      setChatResponse('Error: Failed to get response');\n    } finally {\n      setIsChatting(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-xl font-bold text-gray-900\">No-Code Workflow Builder</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className={`px-2 py-1 rounded-full text-xs font-medium ${\n                backendStatus === 'connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n              }`}>\n                {backendStatus === 'connected' ? '✅ Backend Connected' : '❌ Backend Disconnected'}\n              </div>\n              <button\n                onClick={createWorkflow}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                Create Workflow\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Workflows Panel */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Workflows</h2>\n            {workflows.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <p className=\"text-gray-500 mb-4\">No workflows created yet</p>\n                <button\n                  onClick={createWorkflow}\n                  className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\"\n                >\n                  Create Your First Workflow\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {workflows.map(workflow => (\n                  <div\n                    key={workflow.id}\n                    onClick={() => setCurrentWorkflow(workflow)}\n                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                      currentWorkflow?.id === workflow.id\n                        ? 'border-blue-500 bg-blue-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <h3 className=\"font-medium text-gray-900\">{workflow.name}</h3>\n                    <p className=\"text-sm text-gray-600\">{workflow.description}</p>\n                    <div className=\"mt-2 flex items-center space-x-4 text-xs text-gray-500\">\n                      <span>Components: {workflow.components.length}</span>\n                      <span>Connections: {workflow.connections.length}</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Chat Panel */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Test Chat</h2>\n            {currentWorkflow ? (\n              <div className=\"space-y-4\">\n                <div className=\"p-3 bg-gray-50 rounded-lg\">\n                  <p className=\"text-sm text-gray-600\">Testing workflow: <strong>{currentWorkflow.name}</strong></p>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Your Message</label>\n                  <textarea\n                    value={chatMessage}\n                    onChange={(e) => setChatMessage(e.target.value)}\n                    placeholder=\"Type your message here...\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    rows={3}\n                  />\n                </div>\n                <button\n                  onClick={testChat}\n                  disabled={!chatMessage.trim() || isChatting}\n                  className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n                >\n                  {isChatting ? 'Processing...' : 'Send Message'}\n                </button>\n                {chatResponse && (\n                  <div className=\"mt-4 p-4 bg-gray-50 rounded-lg\">\n                    <h4 className=\"text-sm font-medium text-gray-700 mb-2\">AI Response:</h4>\n                    <p className=\"text-gray-900\">{chatResponse}</p>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"text-center py-8\">\n                <p className=\"text-gray-500\">Select a workflow to test chat functionality</p>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Status Panel */}\n        <div className=\"mt-8 bg-white rounded-lg shadow p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">System Status</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n              <div className=\"text-2xl mb-2\">🌐</div>\n              <h3 className=\"font-medium text-green-800\">Frontend</h3>\n              <p className=\"text-sm text-green-600\">Running on port 3000</p>\n            </div>\n            <div className={`text-center p-4 rounded-lg ${\n              backendStatus === 'connected' ? 'bg-green-50' : 'bg-red-50'\n            }`}>\n              <div className=\"text-2xl mb-2\">{backendStatus === 'connected' ? '🔧' : '❌'}</div>\n              <h3 className={`font-medium ${backendStatus === 'connected' ? 'text-green-800' : 'text-red-800'}`}>\n                Backend API\n              </h3>\n              <p className={`text-sm ${backendStatus === 'connected' ? 'text-green-600' : 'text-red-600'}`}>\n                {backendStatus === 'connected' ? 'Running on port 8000' : 'Not connected'}\n              </p>\n            </div>\n            <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n              <div className=\"text-2xl mb-2\">🤖</div>\n              <h3 className=\"font-medium text-blue-800\">OpenAI</h3>\n              <p className=\"text-sm text-blue-600\">API Key Configured</p>\n            </div>\n          </div>\n          <div className=\"mt-4 text-center\">\n            <a\n              href=\"http://localhost:8000/docs\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-blue-600 hover:text-blue-800 text-sm\"\n            >\n              📚 View API Documentation\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACQ,eAAe,EAAEC,kBAAkB,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACdmB,KAAK,CAAC,8BAA8B,CAAC,CAClCC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MACZX,gBAAgB,CAAC,WAAW,CAAC;MAC7BY,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,CACDC,KAAK,CAAC,MAAM;MACXb,gBAAgB,CAAC,cAAc,CAAC;MAChCF,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMc,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMP,KAAK,CAAC,sCAAsC,CAAC;MACpE,MAAMI,IAAI,GAAG,MAAMG,QAAQ,CAACJ,IAAI,CAAC,CAAC;MAClChB,YAAY,CAACiB,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRjB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,MAAMC,IAAI,GAAGC,MAAM,CAAC,sBAAsB,CAAC;IAC3C,IAAI,CAACD,IAAI,EAAE;IAEX,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAMP,KAAK,CAAC,sCAAsC,EAAE;QACnEa,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEN,IAAI;UAAEO,WAAW,EAAE;QAAoB,CAAC;MACjE,CAAC,CAAC;MACF,MAAMC,WAAW,GAAG,MAAMZ,QAAQ,CAACJ,IAAI,CAAC,CAAC;MACzChB,YAAY,CAAC,CAAC,GAAGD,SAAS,EAAEiC,WAAW,CAAC,CAAC;MACzC9B,kBAAkB,CAAC8B,WAAW,CAAC;IACjC,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdY,KAAK,CAAC,2BAA2B,CAAC;IACpC;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAACjC,eAAe,IAAI,CAACM,WAAW,CAAC4B,IAAI,CAAC,CAAC,EAAE;IAE7CvB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMP,KAAK,CAAC,iCAAiC,EAAE;QAC9Da,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBM,WAAW,EAAEnC,eAAe,CAACoC,EAAE;UAC/BC,OAAO,EAAE/B;QACX,CAAC;MACH,CAAC,CAAC;MACF,MAAMU,IAAI,GAAG,MAAMG,QAAQ,CAACJ,IAAI,CAAC,CAAC;MAClCN,eAAe,CAACO,IAAI,CAACG,QAAQ,CAAC;IAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdX,eAAe,CAAC,+BAA+B,CAAC;IAClD,CAAC,SAAS;MACRE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,IAAIT,SAAS,EAAE;IACb,oBACEP,OAAA;MAAK2C,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE5C,OAAA;QAAK2C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5C,OAAA;UAAK2C,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGhD,OAAA;UAAG2C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhD,OAAA;IAAK2C,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtC5C,OAAA;MAAK2C,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C5C,OAAA;QAAK2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD5C,OAAA;UAAK2C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5C,OAAA;YAAK2C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5C,OAAA;cAAK2C,SAAS,EAAC,iEAAiE;cAAAC,QAAA,eAC9E5C,OAAA;gBAAK2C,SAAS,EAAC,oBAAoB;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAP,QAAA,eACvF5C,OAAA;kBAAMoD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA4B;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhD,OAAA;cAAI2C,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5C,OAAA;cAAK2C,SAAS,EAAE,8CACdlC,aAAa,KAAK,WAAW,GAAG,6BAA6B,GAAG,yBAAyB,EACxF;cAAAmC,QAAA,EACAnC,aAAa,KAAK,WAAW,GAAG,qBAAqB,GAAG;YAAwB;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACNhD,OAAA;cACEwD,OAAO,EAAE7B,cAAe;cACxBgB,SAAS,EAAC,iFAAiF;cAAAC,QAAA,EAC5F;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAK2C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1D5C,OAAA;QAAK2C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD5C,OAAA;UAAK2C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C5C,OAAA;YAAI2C,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACtE7C,SAAS,CAACsD,MAAM,KAAK,CAAC,gBACrBzD,OAAA;YAAK2C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B5C,OAAA;cAAG2C,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9DhD,OAAA;cACEwD,OAAO,EAAE7B,cAAe;cACxBgB,SAAS,EAAC,iFAAiF;cAAAC,QAAA,EAC5F;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENhD,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBzC,SAAS,CAACuD,GAAG,CAACC,QAAQ,iBACrB3D,OAAA;cAEEwD,OAAO,EAAEA,CAAA,KAAMlD,kBAAkB,CAACqD,QAAQ,CAAE;cAC5ChB,SAAS,EAAE,0DACT,CAAAtC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoC,EAAE,MAAKkB,QAAQ,CAAClB,EAAE,GAC/B,4BAA4B,GAC5B,uCAAuC,EAC1C;cAAAG,QAAA,gBAEH5C,OAAA;gBAAI2C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEe,QAAQ,CAAC/B;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9DhD,OAAA;gBAAG2C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEe,QAAQ,CAACxB;cAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/DhD,OAAA;gBAAK2C,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACrE5C,OAAA;kBAAA4C,QAAA,GAAM,cAAY,EAACe,QAAQ,CAACC,UAAU,CAACH,MAAM;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrDhD,OAAA;kBAAA4C,QAAA,GAAM,eAAa,EAACe,QAAQ,CAACE,WAAW,CAACJ,MAAM;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA,GAbDW,QAAQ,CAAClB,EAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNhD,OAAA;UAAK2C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C5C,OAAA;YAAI2C,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACtE3C,eAAe,gBACdL,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5C,OAAA;cAAK2C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxC5C,OAAA;gBAAG2C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,oBAAkB,eAAA5C,OAAA;kBAAA4C,QAAA,EAASvC,eAAe,CAACuB;gBAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eACNhD,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAO2C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpFhD,OAAA;gBACE8D,KAAK,EAAEnD,WAAY;gBACnBoD,QAAQ,EAAGC,CAAC,IAAKpD,cAAc,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDI,WAAW,EAAC,2BAA2B;gBACvCvB,SAAS,EAAC,8HAA8H;gBACxIwB,IAAI,EAAE;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhD,OAAA;cACEwD,OAAO,EAAElB,QAAS;cAClB8B,QAAQ,EAAE,CAACzD,WAAW,CAAC4B,IAAI,CAAC,CAAC,IAAIxB,UAAW;cAC5C4B,SAAS,EAAC,yIAAyI;cAAAC,QAAA,EAElJ7B,UAAU,GAAG,eAAe,GAAG;YAAc;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,EACRnC,YAAY,iBACXb,OAAA;cAAK2C,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7C5C,OAAA;gBAAI2C,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxEhD,OAAA;gBAAG2C,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAE/B;cAAY;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAENhD,OAAA;YAAK2C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B5C,OAAA;cAAG2C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClD5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EhD,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD5C,OAAA;YAAK2C,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD5C,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvChD,OAAA;cAAI2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDhD,OAAA;cAAG2C,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAE,8BACdlC,aAAa,KAAK,WAAW,GAAG,aAAa,GAAG,WAAW,EAC1D;YAAAmC,QAAA,gBACD5C,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEnC,aAAa,KAAK,WAAW,GAAG,IAAI,GAAG;YAAG;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjFhD,OAAA;cAAI2C,SAAS,EAAE,eAAelC,aAAa,KAAK,WAAW,GAAG,gBAAgB,GAAG,cAAc,EAAG;cAAAmC,QAAA,EAAC;YAEnG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhD,OAAA;cAAG2C,SAAS,EAAE,WAAWlC,aAAa,KAAK,WAAW,GAAG,gBAAgB,GAAG,cAAc,EAAG;cAAAmC,QAAA,EAC1FnC,aAAa,KAAK,WAAW,GAAG,sBAAsB,GAAG;YAAe;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5C,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvChD,OAAA;cAAI2C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDhD,OAAA;cAAG2C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B5C,OAAA;YACEqE,IAAI,EAAC,4BAA4B;YACjCJ,MAAM,EAAC,QAAQ;YACfK,GAAG,EAAC,qBAAqB;YACzB3B,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EACtD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9C,EAAA,CA7OQD,GAAG;AAAAsE,EAAA,GAAHtE,GAAG;AA+OZ,eAAeA,GAAG;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}