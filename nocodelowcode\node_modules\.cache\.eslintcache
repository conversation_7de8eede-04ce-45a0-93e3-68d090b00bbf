[{"D:\\assignment for AI planet\\noCodeLowCode\\nocodelowcode\\src\\index.js": "1", "D:\\assignment for AI planet\\noCodeLowCode\\nocodelowcode\\src\\reportWebVitals.js": "2", "D:\\assignment for AI planet\\noCodeLowCode\\nocodelowcode\\src\\App.tsx": "3", "D:\\assignment for AI planet\\noCodeLowCode\\nocodelowcode\\src\\App.js": "4"}, {"size": 535, "mtime": 1750768039387, "results": "5", "hashOfConfig": "6"}, {"size": 362, "mtime": 1750765819896, "results": "7", "hashOfConfig": "6"}, {"size": 1573, "mtime": 1750767395903, "results": "8", "hashOfConfig": "6"}, {"size": 9902, "mtime": 1750768378449, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "17r0kyn", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\assignment for AI planet\\noCodeLowCode\\nocodelowcode\\src\\index.js", [], [], "D:\\assignment for AI planet\\noCodeLowCode\\nocodelowcode\\src\\reportWebVitals.js", [], [], "D:\\assignment for AI planet\\noCodeLowCode\\nocodelowcode\\src\\App.tsx", [], [], "D:\\assignment for AI planet\\noCodeLowCode\\nocodelowcode\\src\\App.js", [], []]