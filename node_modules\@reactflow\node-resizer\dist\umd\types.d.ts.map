{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../packages/node-resizer/src/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACtD,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAE5D,MAAM,MAAM,YAAY,GAAG;IACzB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,yBAAyB,GAAG,YAAY,GAAG;IACrD,SAAS,EAAE,MAAM,EAAE,CAAC;CACrB,CAAC;AAEF,KAAK,eAAe,CAAC,MAAM,GAAG,YAAY,EAAE,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,eAAe,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC;AAEhH,MAAM,MAAM,YAAY,GAAG,eAAe,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;AAC/E,MAAM,MAAM,aAAa,GAAG,eAAe,CAAC;AAC5C,MAAM,MAAM,QAAQ,GAAG,eAAe,CAAC,yBAAyB,CAAC,CAAC;AAClE,MAAM,MAAM,WAAW,GAAG,eAAe,CAAC;AAE1C,MAAM,MAAM,gBAAgB,GAAG;IAC7B,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,WAAW,CAAC,EAAE,aAAa,CAAC;IAC5B,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,SAAS,CAAC,EAAE,aAAa,CAAC;IAC1B,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB,WAAW,CAAC,EAAE,WAAW,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC;AAEtE,MAAM,MAAM,eAAe,GAAG,mBAAmB,GAAG,UAAU,GAAG,WAAW,GAAG,aAAa,GAAG,cAAc,CAAC;AAE9G,oBAAY,oBAAoB;IAC9B,IAAI,SAAS;IACb,MAAM,WAAW;CAClB;AAED,MAAM,MAAM,kBAAkB,GAAG,IAAI,CACnC,gBAAgB,EACd,QAAQ,GACR,OAAO,GACP,UAAU,GACV,WAAW,GACX,UAAU,GACV,WAAW,GACX,iBAAiB,GACjB,cAAc,GACd,eAAe,GACf,UAAU,GACV,aAAa,CAChB,GAAG;IACF,QAAQ,CAAC,EAAE,eAAe,CAAC;IAC3B,OAAO,CAAC,EAAE,oBAAoB,CAAC;IAC/B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,KAAK,CAAC,EAAE,aAAa,CAAC;IACtB,QAAQ,CAAC,EAAE,SAAS,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAAG,kBAAkB,GAAG;IACxD,QAAQ,CAAC,EAAE,mBAAmB,CAAC;CAChC,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG,WAAW,CAAC,cAAc,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC"}