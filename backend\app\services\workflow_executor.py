from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from app.models.workflow import Workflow, WorkflowComponent, WorkflowConnection
from app.services.llm_service import LLMService
from app.services.web_search_service import WebSearchService
from app.services.document_processor import DocumentProcessor

class WorkflowExecutor:
    def __init__(self):
        self.llm_service = LLMService()
        self.web_search_service = WebSearchService()
        self.document_processor = DocumentProcessor()
    
    async def execute_workflow(
        self,
        workflow_id: int,
        query: str,
        db: Session
    ) -> Dict[str, Any]:
        """Execute a workflow with the given query"""
        
        # Get workflow and its components
        workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if not workflow:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        if not workflow.is_active:
            raise ValueError(f"Workflow {workflow_id} is not active")
        
        # Build execution graph
        execution_graph = self._build_execution_graph(workflow)
        
        # Execute workflow
        execution_context = {"query": query, "all_components": workflow.components}
        component_results = {}

        # Find entry point (User Query component)
        entry_component = self._find_entry_component(workflow.components)
        if not entry_component:
            raise ValueError("No User Query component found in workflow")

        # Execute components in order
        result = await self._execute_component_chain(
            entry_component,
            execution_graph,
            execution_context,
            component_results,
            db
        )
        
        return {
            "response": result,
            "component_results": component_results
        }
    
    def _build_execution_graph(self, workflow: Workflow) -> Dict[int, List[int]]:
        """Build a graph of component connections"""
        graph = {}
        
        # Initialize graph with all components
        for component in workflow.components:
            graph[component.id] = []
        
        # Add connections
        for connection in workflow.connections:
            if connection.source_component_id in graph:
                graph[connection.source_component_id].append(connection.target_component_id)
        
        return graph
    
    def _find_entry_component(self, components: List[WorkflowComponent]) -> Optional[WorkflowComponent]:
        """Find the User Query component (entry point)"""
        for component in components:
            if component.component_type == "user_query":
                return component
        return None
    
    async def _execute_component_chain(
        self,
        current_component: WorkflowComponent,
        execution_graph: Dict[int, List[int]],
        execution_context: Dict[str, Any],
        component_results: Dict[str, Any],
        db: Session
    ) -> str:
        """Execute a chain of components"""
        
        # Execute current component
        result = await self._execute_component(current_component, execution_context, db)
        component_results[current_component.component_id] = result
        
        # Update execution context
        execution_context[f"{current_component.component_type}_result"] = result
        
        # Find next components
        next_component_ids = execution_graph.get(current_component.id, [])
        
        if not next_component_ids:
            # This is the final component, return its result
            return result.get("output", str(result))
        
        # Execute next components
        final_result = result.get("output", str(result))
        
        for next_component_id in next_component_ids:
            # Find the component object
            next_component = None
            for comp in execution_context.get("all_components", []):
                if comp.id == next_component_id:
                    next_component = comp
                    break
            
            if next_component:
                final_result = await self._execute_component_chain(
                    next_component,
                    execution_graph,
                    execution_context,
                    component_results,
                    db
                )
        
        return final_result
    
    async def _execute_component(
        self,
        component: WorkflowComponent,
        execution_context: Dict[str, Any],
        db: Session
    ) -> Dict[str, Any]:
        """Execute a single component"""
        
        component_type = component.component_type
        config = component.configuration or {}
        
        if component_type == "user_query":
            return await self._execute_user_query_component(component, execution_context, config)
        elif component_type == "knowledge_base":
            return await self._execute_knowledge_base_component(component, execution_context, config, db)
        elif component_type == "llm_engine":
            return await self._execute_llm_engine_component(component, execution_context, config)
        elif component_type == "output":
            return await self._execute_output_component(component, execution_context, config)
        else:
            raise ValueError(f"Unknown component type: {component_type}")
    
    async def _execute_user_query_component(
        self,
        component: WorkflowComponent,
        execution_context: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute User Query component"""
        query = execution_context.get("query", "")
        
        return {
            "component_type": "user_query",
            "output": query,
            "metadata": {
                "query_length": len(query),
                "component_name": component.name
            }
        }
    
    async def _execute_knowledge_base_component(
        self,
        component: WorkflowComponent,
        execution_context: Dict[str, Any],
        config: Dict[str, Any],
        db: Session
    ) -> Dict[str, Any]:
        """Execute Knowledge Base component"""
        query = execution_context.get("query", "")
        
        # Search documents
        top_k = config.get("top_k", 5)
        similarity_threshold = config.get("similarity_threshold", 0.7)
        
        search_results = await self.document_processor.search_documents(
            query=query,
            top_k=top_k,
            similarity_threshold=similarity_threshold,
            db=db
        )
        
        # Combine search results into context
        context = "\n\n".join([result["content"] for result in search_results])
        
        return {
            "component_type": "knowledge_base",
            "output": context,
            "metadata": {
                "search_results_count": len(search_results),
                "component_name": component.name,
                "search_results": search_results
            }
        }
    
    async def _execute_llm_engine_component(
        self,
        component: WorkflowComponent,
        execution_context: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute LLM Engine component"""
        query = execution_context.get("query", "")
        context = execution_context.get("knowledge_base_result", {}).get("output", "")
        
        # Get configuration
        model = config.get("model", "gpt-3.5-turbo")
        max_tokens = config.get("max_tokens", 1000)
        temperature = config.get("temperature", 0.7)
        custom_prompt = config.get("custom_prompt", "")
        use_web_search = config.get("use_web_search", False)
        
        # Prepare prompt
        prompt = query
        if custom_prompt:
            prompt = custom_prompt.replace("{query}", query)
        
        # Add web search if enabled
        web_search_context = ""
        if use_web_search:
            try:
                search_results = await self.web_search_service.search(query, num_results=3)
                web_search_context = "\n".join([
                    f"Title: {result['title']}\nSnippet: {result['snippet']}"
                    for result in search_results
                ])
            except Exception as e:
                web_search_context = f"Web search failed: {str(e)}"
        
        # Combine all context
        full_context = ""
        if context:
            full_context += f"Knowledge Base Context:\n{context}\n\n"
        if web_search_context:
            full_context += f"Web Search Context:\n{web_search_context}\n\n"
        
        # Generate response
        llm_result = await self.llm_service.generate_text(
            prompt=prompt,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            context=full_context if full_context else None
        )
        
        return {
            "component_type": "llm_engine",
            "output": llm_result["response"],
            "metadata": {
                "model": llm_result["model"],
                "tokens_used": llm_result["tokens_used"],
                "execution_time": llm_result["execution_time"],
                "component_name": component.name,
                "used_web_search": use_web_search
            }
        }
    
    async def _execute_output_component(
        self,
        component: WorkflowComponent,
        execution_context: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Output component"""
        
        # Get the LLM result or previous component result
        llm_result = execution_context.get("llm_engine_result", {})
        output = llm_result.get("output", "No response generated")
        
        # Apply any output formatting from config
        output_format = config.get("format", "text")
        
        return {
            "component_type": "output",
            "output": output,
            "metadata": {
                "format": output_format,
                "component_name": component.name
            }
        }
