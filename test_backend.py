#!/usr/bin/env python3
"""
Simple test script to verify the backend API is working
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health check: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_create_workflow():
    """Test creating a workflow"""
    try:
        workflow_data = {
            "name": "Test Workflow",
            "description": "A test workflow for verification"
        }
        response = requests.post(f"{BASE_URL}/api/workflows/", json=workflow_data)
        print(f"Create workflow: {response.status_code}")
        if response.status_code == 200:
            workflow = response.json()
            print(f"Created workflow ID: {workflow['id']}")
            return workflow
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Create workflow failed: {e}")
        return None

def test_add_component(workflow_id):
    """Test adding a component to workflow"""
    try:
        component_data = {
            "component_type": "user_query",
            "component_id": "user_query_1",
            "name": "User Query",
            "position_x": 100,
            "position_y": 100,
            "configuration": {}
        }
        response = requests.post(f"{BASE_URL}/api/workflows/{workflow_id}/components", json=component_data)
        print(f"Add component: {response.status_code}")
        if response.status_code == 200:
            component = response.json()
            print(f"Created component ID: {component['id']}")
            return component
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Add component failed: {e}")
        return None

def test_llm_models():
    """Test getting available LLM models"""
    try:
        response = requests.get(f"{BASE_URL}/api/llm/models")
        print(f"Get LLM models: {response.status_code}")
        if response.status_code == 200:
            models = response.json()
            print(f"Available models: {json.dumps(models, indent=2)}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Get LLM models failed: {e}")
        return False

def main():
    print("Testing No-Code/Low-Code Backend API")
    print("=" * 50)
    
    # Test health
    if not test_health():
        print("❌ Backend is not running or not healthy")
        return
    
    print("✅ Backend is healthy")
    
    # Test workflow creation
    workflow = test_create_workflow()
    if not workflow:
        print("❌ Failed to create workflow")
        return
    
    print("✅ Workflow created successfully")
    
    # Test component addition
    component = test_add_component(workflow['id'])
    if not component:
        print("❌ Failed to add component")
        return
    
    print("✅ Component added successfully")
    
    # Test LLM models
    if test_llm_models():
        print("✅ LLM models endpoint working")
    else:
        print("⚠️  LLM models endpoint not working (may need API keys)")
    
    print("\n" + "=" * 50)
    print("✅ Basic backend functionality verified!")
    print(f"🌐 Frontend should be available at: http://localhost:3000")
    print(f"📚 API docs available at: {BASE_URL}/docs")

if __name__ == "__main__":
    main()
