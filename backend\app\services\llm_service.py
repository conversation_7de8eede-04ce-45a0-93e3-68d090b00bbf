from openai import OpenAI
import google.generativeai as genai
import time
from typing import List, Optional
from app.core.config import settings

class LLMService:
    def __init__(self):
        # Configure OpenAI
        self.openai_client = None
        if settings.OPENAI_API_KEY:
            self.openai_client = OpenAI(api_key=settings.OPENAI_API_KEY)

        # Configure Gemini
        if settings.GEMINI_API_KEY:
            genai.configure(api_key=settings.GEMINI_API_KEY)
    
    async def generate_text(
        self,
        prompt: str,
        model: str = "gpt-3.5-turbo",
        max_tokens: int = 1000,
        temperature: float = 0.7,
        context: Optional[str] = None
    ) -> dict:
        """Generate text using specified LLM model"""
        start_time = time.time()
        
        # Prepare the full prompt with context
        full_prompt = prompt
        if context:
            full_prompt = f"Context: {context}\n\nQuestion: {prompt}"
        
        try:
            if model.startswith("gpt"):
                return await self._generate_openai(full_prompt, model, max_tokens, temperature)
            elif model.startswith("gemini"):
                return await self._generate_gemini(full_prompt, model, max_tokens, temperature)
            else:
                raise ValueError(f"Unsupported model: {model}")
        except Exception as e:
            execution_time = time.time() - start_time
            raise Exception(f"LLM generation failed: {str(e)}")
    
    async def _generate_openai(self, prompt: str, model: str, max_tokens: int, temperature: float) -> dict:
        """Generate text using OpenAI models"""
        if not self.openai_client:
            raise ValueError("OpenAI client not configured")

        start_time = time.time()

        response = self.openai_client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=max_tokens,
            temperature=temperature
        )

        execution_time = time.time() - start_time

        return {
            "response": response.choices[0].message.content,
            "model": model,
            "tokens_used": response.usage.total_tokens,
            "execution_time": execution_time
        }
    
    async def _generate_gemini(self, prompt: str, model: str, max_tokens: int, temperature: float) -> dict:
        """Generate text using Gemini models"""
        start_time = time.time()
        
        # Configure generation parameters
        generation_config = {
            "temperature": temperature,
            "max_output_tokens": max_tokens,
        }
        
        # Initialize the model
        gemini_model = genai.GenerativeModel(model)
        
        # Generate response
        response = gemini_model.generate_content(
            prompt,
            generation_config=generation_config
        )
        
        execution_time = time.time() - start_time
        
        return {
            "response": response.text,
            "model": model,
            "tokens_used": len(response.text.split()),  # Approximate token count
            "execution_time": execution_time
        }
    
    async def generate_embeddings(self, text: str, model: str = "text-embedding-ada-002") -> List[float]:
        """Generate embeddings for text"""
        if not self.openai_client:
            raise ValueError("OpenAI client not configured")

        if model.startswith("text-embedding"):
            # OpenAI embeddings
            response = self.openai_client.embeddings.create(
                model=model,
                input=text
            )
            return response.data[0].embedding
        else:
            raise ValueError(f"Unsupported embedding model: {model}")

    async def generate_embeddings_batch(self, texts: List[str], model: str = "text-embedding-ada-002") -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        if not self.openai_client:
            raise ValueError("OpenAI client not configured")

        if model.startswith("text-embedding"):
            # OpenAI embeddings
            response = self.openai_client.embeddings.create(
                model=model,
                input=texts
            )
            return [item.embedding for item in response.data]
        else:
            raise ValueError(f"Unsupported embedding model: {model}")
