import { useState, useEffect } from 'react';
import { Workflow } from '../types';
import { workflowApi } from '../services/api';
import toast from 'react-hot-toast';

export const useWorkflows = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [currentWorkflow, setCurrentWorkflow] = useState<Workflow | undefined>();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load workflows on mount
  useEffect(() => {
    loadWorkflows();
  }, []);

  const loadWorkflows = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await workflowApi.getWorkflows();
      setWorkflows(data);
    } catch (err) {
      console.error('Failed to load workflows:', err);
      setError('Failed to load workflows');
      toast.error('Failed to load workflows');
    } finally {
      setIsLoading(false);
    }
  };

  const createWorkflow = async (name: string, description?: string) => {
    try {
      const newWorkflow = await workflowApi.createWorkflow({
        name,
        description,
        is_active: true,
      });
      
      setWorkflows(prev => [...prev, newWorkflow]);
      setCurrentWorkflow(newWorkflow);
      toast.success('Workflow created successfully');
      return newWorkflow;
    } catch (err) {
      console.error('Failed to create workflow:', err);
      toast.error('Failed to create workflow');
      throw err;
    }
  };

  const updateWorkflow = async (id: number, updates: Partial<Workflow>) => {
    try {
      const updatedWorkflow = await workflowApi.updateWorkflow(id, updates);
      
      setWorkflows(prev => 
        prev.map(w => w.id === id ? updatedWorkflow : w)
      );
      
      if (currentWorkflow?.id === id) {
        setCurrentWorkflow(updatedWorkflow);
      }
      
      return updatedWorkflow;
    } catch (err) {
      console.error('Failed to update workflow:', err);
      toast.error('Failed to update workflow');
      throw err;
    }
  };

  const deleteWorkflow = async (id: number) => {
    try {
      await workflowApi.deleteWorkflow(id);
      
      setWorkflows(prev => prev.filter(w => w.id !== id));
      
      if (currentWorkflow?.id === id) {
        setCurrentWorkflow(undefined);
      }
      
      toast.success('Workflow deleted successfully');
    } catch (err) {
      console.error('Failed to delete workflow:', err);
      toast.error('Failed to delete workflow');
      throw err;
    }
  };

  const selectWorkflow = async (workflow: Workflow) => {
    try {
      // Fetch the latest version of the workflow with all components and connections
      const fullWorkflow = await workflowApi.getWorkflow(workflow.id);
      setCurrentWorkflow(fullWorkflow);
    } catch (err) {
      console.error('Failed to load workflow details:', err);
      toast.error('Failed to load workflow details');
      // Fallback to the provided workflow
      setCurrentWorkflow(workflow);
    }
  };

  const refreshCurrentWorkflow = async () => {
    if (!currentWorkflow) return;
    
    try {
      const refreshedWorkflow = await workflowApi.getWorkflow(currentWorkflow.id);
      setCurrentWorkflow(refreshedWorkflow);
      
      // Also update in the workflows list
      setWorkflows(prev => 
        prev.map(w => w.id === refreshedWorkflow.id ? refreshedWorkflow : w)
      );
    } catch (err) {
      console.error('Failed to refresh workflow:', err);
      toast.error('Failed to refresh workflow');
    }
  };

  return {
    workflows,
    currentWorkflow,
    isLoading,
    error,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    selectWorkflow,
    refreshCurrentWorkflow,
    setCurrentWorkflow,
  };
};
