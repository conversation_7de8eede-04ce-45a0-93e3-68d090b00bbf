import React, { useCallback, useRef, useState } from 'react';
import React<PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  ReactFlowProvider,
  ReactFlowInstance,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { Workflow, WorkflowComponent, WorkflowConnection } from '../../types';
import WorkflowNode from './WorkflowNode';
import { workflowApi } from '../../services/api';
import toast from 'react-hot-toast';

const nodeTypes = {
  workflowNode: WorkflowNode,
};

interface WorkflowWorkspaceProps {
  workflow: Workflow;
  onWorkflowChange: (workflow: Workflow) => void;
  onComponentSelect: (componentId: string | null) => void;
  selectedComponentId: string | null;
}

const WorkflowWorkspace: React.FC<WorkflowWorkspaceProps> = ({
  workflow,
  onWorkflowChange,
  onComponentSelect,
  selectedComponentId,
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);

  // Convert workflow components to React Flow nodes
  const initialNodes: Node[] = workflow.components.map((component) => ({
    id: component.component_id,
    type: 'workflowNode',
    position: { x: component.position_x, y: component.position_y },
    data: {
      label: component.name,
      componentType: component.component_type,
      configuration: component.configuration,
      dbId: component.id,
      isSelected: component.component_id === selectedComponentId,
    },
  }));

  // Convert workflow connections to React Flow edges
  const initialEdges: Edge[] = workflow.connections.map((connection) => {
    const sourceComponent = workflow.components.find(c => c.id === connection.source_component_id);
    const targetComponent = workflow.components.find(c => c.id === connection.target_component_id);
    
    return {
      id: connection.connection_id,
      source: sourceComponent?.component_id || '',
      target: targetComponent?.component_id || '',
      type: 'smoothstep',
      animated: true,
    };
  });

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // Handle new connections
  const onConnect = useCallback(
    async (params: Connection) => {
      if (!params.source || !params.target) return;

      const sourceComponent = workflow.components.find(c => c.component_id === params.source);
      const targetComponent = workflow.components.find(c => c.component_id === params.target);

      if (!sourceComponent || !targetComponent) return;

      try {
        const connectionId = `${params.source}-${params.target}`;
        
        // Create connection in backend
        const newConnection = await workflowApi.createConnection(workflow.id, {
          connection_id: connectionId,
          source_component_id: sourceComponent.id,
          target_component_id: targetComponent.id,
        });

        // Update local state
        const newEdge = {
          id: connectionId,
          source: params.source,
          target: params.target,
          type: 'smoothstep',
          animated: true,
        };

        setEdges((eds) => addEdge(newEdge, eds));

        // Update workflow
        const updatedWorkflow = {
          ...workflow,
          connections: [...workflow.connections, newConnection],
        };
        onWorkflowChange(updatedWorkflow);

        toast.success('Connection created');
      } catch (error) {
        console.error('Failed to create connection:', error);
        toast.error('Failed to create connection');
      }
    },
    [workflow, onWorkflowChange, setEdges]
  );

  // Handle drag over
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // Handle drop to create new component
  const onDrop = useCallback(
    async (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const componentType = event.dataTransfer.getData('application/reactflow');

      if (!componentType || !reactFlowInstance || !reactFlowBounds) return;

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      try {
        const componentId = `${componentType}-${Date.now()}`;
        const componentName = componentType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

        // Create component in backend
        const newComponent = await workflowApi.createComponent(workflow.id, {
          component_type: componentType,
          component_id: componentId,
          name: componentName,
          position_x: position.x,
          position_y: position.y,
          configuration: {},
        });

        // Create new node
        const newNode: Node = {
          id: componentId,
          type: 'workflowNode',
          position,
          data: {
            label: componentName,
            componentType: componentType,
            configuration: {},
            dbId: newComponent.id,
            isSelected: false,
          },
        };

        setNodes((nds) => nds.concat(newNode));

        // Update workflow
        const updatedWorkflow = {
          ...workflow,
          components: [...workflow.components, newComponent],
        };
        onWorkflowChange(updatedWorkflow);

        toast.success(`${componentName} component added`);
      } catch (error) {
        console.error('Failed to create component:', error);
        toast.error('Failed to create component');
      }
    },
    [reactFlowInstance, workflow, onWorkflowChange, setNodes]
  );

  // Handle node click
  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      onComponentSelect(node.id);
      
      // Update node selection state
      setNodes((nds) =>
        nds.map((n) => ({
          ...n,
          data: {
            ...n.data,
            isSelected: n.id === node.id,
          },
        }))
      );
    },
    [onComponentSelect, setNodes]
  );

  // Handle node position change
  const onNodeDragStop = useCallback(
    async (event: React.MouseEvent, node: Node) => {
      const component = workflow.components.find(c => c.component_id === node.id);
      if (!component) return;

      try {
        await workflowApi.updateComponent(workflow.id, component.id, {
          position_x: node.position.x,
          position_y: node.position.y,
        });

        // Update workflow
        const updatedComponents = workflow.components.map((comp) =>
          comp.component_id === node.id
            ? { ...comp, position_x: node.position.x, position_y: node.position.y }
            : comp
        );

        const updatedWorkflow = {
          ...workflow,
          components: updatedComponents,
        };
        onWorkflowChange(updatedWorkflow);
      } catch (error) {
        console.error('Failed to update component position:', error);
      }
    },
    [workflow, onWorkflowChange]
  );

  // Handle edge deletion
  const onEdgesDelete = useCallback(
    async (edgesToDelete: Edge[]) => {
      for (const edge of edgesToDelete) {
        try {
          const connection = workflow.connections.find(c => c.connection_id === edge.id);
          if (connection) {
            await workflowApi.deleteConnection(workflow.id, connection.id);
          }
        } catch (error) {
          console.error('Failed to delete connection:', error);
          toast.error('Failed to delete connection');
        }
      }

      // Update workflow
      const remainingConnections = workflow.connections.filter(
        (conn) => !edgesToDelete.some(edge => edge.id === conn.connection_id)
      );

      const updatedWorkflow = {
        ...workflow,
        connections: remainingConnections,
      };
      onWorkflowChange(updatedWorkflow);

      toast.success('Connection deleted');
    },
    [workflow, onWorkflowChange]
  );

  // Handle background click
  const onPaneClick = useCallback(() => {
    onComponentSelect(null);
    
    // Clear all node selections
    setNodes((nds) =>
      nds.map((n) => ({
        ...n,
        data: {
          ...n.data,
          isSelected: false,
        },
      }))
    );
  }, [onComponentSelect, setNodes]);

  return (
    <div className="flex-1 bg-gray-50" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={setReactFlowInstance}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        onNodeDragStop={onNodeDragStop}
        onEdgesDelete={onEdgesDelete}
        onPaneClick={onPaneClick}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="top-right"
      >
        <Controls />
        <MiniMap />
        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
      </ReactFlow>
    </div>
  );
};

const WorkflowWorkspaceWrapper: React.FC<WorkflowWorkspaceProps> = (props) => (
  <ReactFlowProvider>
    <WorkflowWorkspace {...props} />
  </ReactFlowProvider>
);

export default WorkflowWorkspaceWrapper;
