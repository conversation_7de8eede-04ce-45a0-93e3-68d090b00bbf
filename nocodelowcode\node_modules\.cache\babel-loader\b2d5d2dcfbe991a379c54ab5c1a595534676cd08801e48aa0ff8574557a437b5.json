{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\noCodeLowCode\\\\nocodelowcode\\\\src\\\\components\\\\workflow\\\\ComponentPanel.js\";\nimport React from 'react';\nimport { ComponentTypes, ComponentMetadata } from '../../types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComponentPanel = () => {\n  const componentTypes = [{\n    id: ComponentTypes.USER_QUERY,\n    ...ComponentMetadata[ComponentTypes.USER_QUERY]\n  }, {\n    id: ComponentTypes.KNOWLEDGE_BASE,\n    ...ComponentMetadata[ComponentTypes.KNOWLEDGE_BASE]\n  }, {\n    id: ComponentTypes.LLM_ENGINE,\n    ...ComponentMetadata[ComponentTypes.LLM_ENGINE]\n  }, {\n    id: ComponentTypes.OUTPUT,\n    ...ComponentMetadata[ComponentTypes.OUTPUT]\n  }];\n  const handleDragStart = (event, componentType) => {\n    event.dataTransfer.setData('application/reactflow', componentType);\n    event.dataTransfer.effectAllowed = 'move';\n\n    // Add visual feedback\n    event.target.style.opacity = '0.5';\n  };\n  const handleDragEnd = event => {\n    event.target.style.opacity = '1';\n  };\n  const getColorClasses = color => {\n    const colorMap = {\n      blue: 'bg-blue-500 text-white',\n      green: 'bg-green-500 text-white',\n      purple: 'bg-purple-500 text-white',\n      orange: 'bg-orange-500 text-white'\n    };\n    return colorMap[color] || 'bg-gray-500 text-white';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4 space-y-4\",\n    children: [componentTypes.map(component => /*#__PURE__*/_jsxDEV(\"div\", {\n      draggable: true,\n      onDragStart: event => handleDragStart(event, component.id),\n      onDragEnd: handleDragEnd,\n      className: \"group cursor-move bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${getColorClasses(component.color)} p-2 rounded-lg flex-shrink-0 text-lg`,\n          children: component.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-semibold text-gray-900 group-hover:text-blue-700\",\n            children: component.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-600 mt-1 leading-relaxed\",\n            children: component.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-1 h-1 bg-gray-400 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-1 h-1 bg-gray-400 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-1 h-1 bg-gray-400 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this)]\n    }, component.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 p-4 bg-gray-50 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-sm font-medium text-gray-900 mb-2\",\n        children: \"How to use:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"text-xs text-gray-600 space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 Drag components to the workspace\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 Configure each component's settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 Test your workflow with \\\"Chat with Stack\\\"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 p-4 bg-blue-50 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-sm font-medium text-blue-900 mb-2\",\n        children: \"Workflow Guidelines:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"text-xs text-blue-700 space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 Start with a User Query component\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 End with an Output component\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 Knowledge Base is optional but recommended\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 LLM Engine processes the final response\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_c = ComponentPanel;\nexport default ComponentPanel;\nvar _c;\n$RefreshReg$(_c, \"ComponentPanel\");", "map": {"version": 3, "names": ["React", "ComponentTypes", "ComponentMetadata", "jsxDEV", "_jsxDEV", "ComponentPanel", "componentTypes", "id", "USER_QUERY", "KNOWLEDGE_BASE", "LLM_ENGINE", "OUTPUT", "handleDragStart", "event", "componentType", "dataTransfer", "setData", "effectAllowed", "target", "style", "opacity", "handleDragEnd", "getColorClasses", "color", "colorMap", "blue", "green", "purple", "orange", "className", "children", "map", "component", "draggable", "onDragStart", "onDragEnd", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "description", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/noCodeLowCode/nocodelowcode/src/components/workflow/ComponentPanel.js"], "sourcesContent": ["import React from 'react';\nimport { ComponentTypes, ComponentMetadata } from '../../types';\n\nconst ComponentPanel = () => {\n  const componentTypes = [\n    {\n      id: ComponentTypes.USER_QUERY,\n      ...ComponentMetadata[ComponentTypes.USER_QUERY],\n    },\n    {\n      id: ComponentTypes.KNOWLEDGE_BASE,\n      ...ComponentMetadata[ComponentTypes.KNOWLEDGE_BASE],\n    },\n    {\n      id: ComponentTypes.LLM_ENGINE,\n      ...ComponentMetadata[ComponentTypes.LLM_ENGINE],\n    },\n    {\n      id: ComponentTypes.OUTPUT,\n      ...ComponentMetadata[ComponentTypes.OUTPUT],\n    },\n  ];\n\n  const handleDragStart = (event, componentType) => {\n    event.dataTransfer.setData('application/reactflow', componentType);\n    event.dataTransfer.effectAllowed = 'move';\n\n    // Add visual feedback\n    event.target.style.opacity = '0.5';\n  };\n\n  const handleDragEnd = (event) => {\n    event.target.style.opacity = '1';\n  };\n\n  const getColorClasses = (color) => {\n    const colorMap = {\n      blue: 'bg-blue-500 text-white',\n      green: 'bg-green-500 text-white',\n      purple: 'bg-purple-500 text-white',\n      orange: 'bg-orange-500 text-white',\n    };\n    return colorMap[color] || 'bg-gray-500 text-white';\n  };\n\n  return (\n    <div className=\"p-4 space-y-4\">\n      {componentTypes.map((component) => (\n        <div\n          key={component.id}\n          draggable\n          onDragStart={(event) => handleDragStart(event, component.id)}\n          onDragEnd={handleDragEnd}\n          className=\"group cursor-move bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105\"\n        >\n          <div className=\"flex items-start space-x-3\">\n            <div className={`${getColorClasses(component.color)} p-2 rounded-lg flex-shrink-0 text-lg`}>\n              {component.icon}\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <h3 className=\"text-sm font-semibold text-gray-900 group-hover:text-blue-700\">\n                {component.name}\n              </h3>\n              <p className=\"text-xs text-gray-600 mt-1 leading-relaxed\">\n                {component.description}\n              </p>\n            </div>\n          </div>\n\n          {/* Drag indicator */}\n          <div className=\"mt-3 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\">\n            <div className=\"flex space-x-1\">\n              <div className=\"w-1 h-1 bg-gray-400 rounded-full\"></div>\n              <div className=\"w-1 h-1 bg-gray-400 rounded-full\"></div>\n              <div className=\"w-1 h-1 bg-gray-400 rounded-full\"></div>\n            </div>\n          </div>\n        </div>\n      ))}\n\n      {/* Instructions */}\n      <div className=\"mt-8 p-4 bg-gray-50 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-gray-900 mb-2\">\n          How to use:\n        </h4>\n        <ul className=\"text-xs text-gray-600 space-y-1\">\n          <li>• Drag components to the workspace</li>\n          <li>• Configure each component's settings</li>\n          <li>• Test your workflow with \"Chat with Stack\"</li>\n        </ul>\n      </div>\n\n      {/* Component Guidelines */}\n      <div className=\"mt-4 p-4 bg-blue-50 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-blue-900 mb-2\">\n          Workflow Guidelines:\n        </h4>\n        <ul className=\"text-xs text-blue-700 space-y-1\">\n          <li>• Start with a User Query component</li>\n          <li>• End with an Output component</li>\n          <li>• Knowledge Base is optional but recommended</li>\n          <li>• LLM Engine processes the final response</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default ComponentPanel;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,cAAc,GAAG,CACrB;IACEC,EAAE,EAAEN,cAAc,CAACO,UAAU;IAC7B,GAAGN,iBAAiB,CAACD,cAAc,CAACO,UAAU;EAChD,CAAC,EACD;IACED,EAAE,EAAEN,cAAc,CAACQ,cAAc;IACjC,GAAGP,iBAAiB,CAACD,cAAc,CAACQ,cAAc;EACpD,CAAC,EACD;IACEF,EAAE,EAAEN,cAAc,CAACS,UAAU;IAC7B,GAAGR,iBAAiB,CAACD,cAAc,CAACS,UAAU;EAChD,CAAC,EACD;IACEH,EAAE,EAAEN,cAAc,CAACU,MAAM;IACzB,GAAGT,iBAAiB,CAACD,cAAc,CAACU,MAAM;EAC5C,CAAC,CACF;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,aAAa,KAAK;IAChDD,KAAK,CAACE,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAEF,aAAa,CAAC;IAClED,KAAK,CAACE,YAAY,CAACE,aAAa,GAAG,MAAM;;IAEzC;IACAJ,KAAK,CAACK,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,KAAK;EACpC,CAAC;EAED,MAAMC,aAAa,GAAIR,KAAK,IAAK;IAC/BA,KAAK,CAACK,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,GAAG;EAClC,CAAC;EAED,MAAME,eAAe,GAAIC,KAAK,IAAK;IACjC,MAAMC,QAAQ,GAAG;MACfC,IAAI,EAAE,wBAAwB;MAC9BC,KAAK,EAAE,yBAAyB;MAChCC,MAAM,EAAE,0BAA0B;MAClCC,MAAM,EAAE;IACV,CAAC;IACD,OAAOJ,QAAQ,CAACD,KAAK,CAAC,IAAI,wBAAwB;EACpD,CAAC;EAED,oBACEnB,OAAA;IAAKyB,SAAS,EAAC,eAAe;IAAAC,QAAA,GAC3BxB,cAAc,CAACyB,GAAG,CAAEC,SAAS,iBAC5B5B,OAAA;MAEE6B,SAAS;MACTC,WAAW,EAAGrB,KAAK,IAAKD,eAAe,CAACC,KAAK,EAAEmB,SAAS,CAACzB,EAAE,CAAE;MAC7D4B,SAAS,EAAEd,aAAc;MACzBQ,SAAS,EAAC,8JAA8J;MAAAC,QAAA,gBAExK1B,OAAA;QAAKyB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC1B,OAAA;UAAKyB,SAAS,EAAE,GAAGP,eAAe,CAACU,SAAS,CAACT,KAAK,CAAC,uCAAwC;UAAAO,QAAA,EACxFE,SAAS,CAACI;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACNpC,OAAA;UAAKyB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1B,OAAA;YAAIyB,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAC1EE,SAAS,CAACS;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACLpC,OAAA;YAAGyB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EACtDE,SAAS,CAACU;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpC,OAAA;QAAKyB,SAAS,EAAC,4FAA4F;QAAAC,QAAA,eACzG1B,OAAA;UAAKyB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1B,OAAA;YAAKyB,SAAS,EAAC;UAAkC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDpC,OAAA;YAAKyB,SAAS,EAAC;UAAkC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDpC,OAAA;YAAKyB,SAAS,EAAC;UAAkC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,GA3BDR,SAAS,CAACzB,EAAE;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA4Bd,CACN,CAAC,eAGFpC,OAAA;MAAKyB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7C1B,OAAA;QAAIyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAEvD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpC,OAAA;QAAIyB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC7C1B,OAAA;UAAA0B,QAAA,EAAI;QAAkC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3CpC,OAAA;UAAA0B,QAAA,EAAI;QAAqC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9CpC,OAAA;UAAA0B,QAAA,EAAI;QAA2C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNpC,OAAA;MAAKyB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7C1B,OAAA;QAAIyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAEvD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpC,OAAA;QAAIyB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC7C1B,OAAA;UAAA0B,QAAA,EAAI;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CpC,OAAA;UAAA0B,QAAA,EAAI;QAA8B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvCpC,OAAA;UAAA0B,QAAA,EAAI;QAA4C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrDpC,OAAA;UAAA0B,QAAA,EAAI;QAAyC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GAvGItC,cAAc;AAyGpB,eAAeA,cAAc;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}