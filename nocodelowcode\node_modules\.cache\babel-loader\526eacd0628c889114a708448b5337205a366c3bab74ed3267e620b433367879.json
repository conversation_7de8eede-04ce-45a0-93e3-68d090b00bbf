{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\noCodeLowCode\\\\nocodelowcode\\\\src\\\\App.js\";\nimport React from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-blue-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: \"No-Code Workflow Builder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Visual workflow creation with AI-powered components\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2 text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2705 Frontend is running successfully\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\uD83D\\uDD27 Backend API: http://localhost:8000\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\uD83D\\uDCDA API Docs: http://localhost:8000/docs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-blue-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Next Steps:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), \"1. Start the backend API\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), \"2. Create your first workflow\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), \"3. Add components and test\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "App", "className", "children", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/noCodeLowCode/nocodelowcode/src/App.js"], "sourcesContent": ["import React from 'react';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\">\n            <svg\n              className=\"w-8 h-8 text-blue-600\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M13 10V3L4 14h7v7l9-11h-7z\"\n              />\n            </svg>\n          </div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n            No-Code Workflow Builder\n          </h1>\n          <p className=\"text-gray-600 mb-4\">\n            Visual workflow creation with AI-powered components\n          </p>\n          <div className=\"space-y-2 text-sm text-gray-500\">\n            <p>✅ Frontend is running successfully</p>\n            <p>🔧 Backend API: http://localhost:8000</p>\n            <p>📚 API Docs: http://localhost:8000/docs</p>\n          </div>\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <p className=\"text-sm text-blue-700\">\n              <strong>Next Steps:</strong>\n              <br />\n              1. Start the backend API\n              <br />\n              2. Create your first workflow\n              <br />\n              3. Add components and test\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,0DAA0D;IAAAC,QAAA,eACvEH,OAAA;MAAKE,SAAS,EAAC,mDAAmD;MAAAC,QAAA,eAChEH,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAKE,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/FH,OAAA;YACEE,SAAS,EAAC,uBAAuB;YACjCE,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YAAAH,QAAA,eAEnBH,OAAA;cACEO,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNd,OAAA;UAAIE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLd,OAAA;UAAGE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJd,OAAA;UAAKE,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CH,OAAA;YAAAG,QAAA,EAAG;UAAkC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzCd,OAAA;YAAAG,QAAA,EAAG;UAAqC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5Cd,OAAA;YAAAG,QAAA,EAAG;UAAuC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNd,OAAA;UAAKE,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7CH,OAAA;YAAGE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCH,OAAA;cAAAG,QAAA,EAAQ;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5Bd,OAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,4BAEN,eAAAd,OAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,iCAEN,eAAAd,OAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,8BAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GA9CQd,GAAG;AAgDZ,eAAeA,GAAG;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}