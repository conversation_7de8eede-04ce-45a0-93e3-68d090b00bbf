from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
from dotenv import load_dotenv
import openai

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="No-Code/Low-Code Workflow Builder API",
    description="API for building and executing intelligent workflows",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize OpenAI
openai_api_key = os.getenv("OPENAI_API_KEY")
if openai_api_key:
    openai_client = openai.OpenAI(api_key=openai_api_key)
else:
    openai_client = None

# In-memory storage for demo
workflows = []
workflow_counter = 1

# Pydantic models
class WorkflowCreate(BaseModel):
    name: str
    description: Optional[str] = None

class Workflow(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    is_active: bool = True
    components: List[Dict[str, Any]] = []
    connections: List[Dict[str, Any]] = []

class ComponentCreate(BaseModel):
    component_type: str
    component_id: str
    name: str
    position_x: int = 0
    position_y: int = 0
    configuration: Dict[str, Any] = {}

class ChatRequest(BaseModel):
    workflow_id: int
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    session_id: str
    message: str
    response: str
    execution_time: float

@app.get("/")
async def root():
    return {"message": "No-Code/Low-Code Workflow Builder API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "openai_configured": openai_client is not None}

# Workflow endpoints
@app.get("/api/workflows/", response_model=List[Workflow])
async def get_workflows():
    return workflows

@app.post("/api/workflows/", response_model=Workflow)
async def create_workflow(workflow: WorkflowCreate):
    global workflow_counter
    new_workflow = Workflow(
        id=workflow_counter,
        name=workflow.name,
        description=workflow.description,
        is_active=True,
        components=[],
        connections=[]
    )
    workflows.append(new_workflow)
    workflow_counter += 1
    return new_workflow

@app.get("/api/workflows/{workflow_id}", response_model=Workflow)
async def get_workflow(workflow_id: int):
    workflow = next((w for w in workflows if w.id == workflow_id), None)
    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")
    return workflow

@app.post("/api/workflows/{workflow_id}/components")
async def create_component(workflow_id: int, component: ComponentCreate):
    workflow = next((w for w in workflows if w.id == workflow_id), None)
    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")
    
    new_component = {
        "id": len(workflow.components) + 1,
        "workflow_id": workflow_id,
        "component_type": component.component_type,
        "component_id": component.component_id,
        "name": component.name,
        "position_x": component.position_x,
        "position_y": component.position_y,
        "configuration": component.configuration,
    }
    
    workflow.components.append(new_component)
    return new_component

# Chat endpoint
@app.post("/api/chat/", response_model=ChatResponse)
async def chat(request: ChatRequest):
    import time
    import uuid
    
    start_time = time.time()
    
    # Find workflow
    workflow = next((w for w in workflows if w.id == request.workflow_id), None)
    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")
    
    # Simple response generation
    if openai_client:
        try:
            response = openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant in a no-code workflow builder."},
                    {"role": "user", "content": request.message}
                ],
                max_tokens=500,
                temperature=0.7
            )
            ai_response = response.choices[0].message.content
        except Exception as e:
            ai_response = f"AI response error: {str(e)}"
    else:
        ai_response = f"Echo: {request.message} (OpenAI not configured)"
    
    execution_time = time.time() - start_time
    session_id = request.session_id or str(uuid.uuid4())
    
    return ChatResponse(
        session_id=session_id,
        message=request.message,
        response=ai_response,
        execution_time=execution_time
    )

# LLM endpoints
@app.get("/api/llm/models")
async def get_models():
    return {
        "openai": [
            "gpt-4",
            "gpt-4-turbo-preview",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ],
        "gemini": [
            "gemini-pro",
            "gemini-pro-vision"
        ]
    }

@app.post("/api/llm/generate")
async def generate_text(prompt: str, model: str = "gpt-3.5-turbo"):
    if not openai_client:
        raise HTTPException(status_code=500, detail="OpenAI not configured")
    
    try:
        response = openai_client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1000,
            temperature=0.7
        )
        
        return {
            "response": response.choices[0].message.content,
            "model": model,
            "tokens_used": response.usage.total_tokens,
            "execution_time": 0.5
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LLM generation failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
