#!/bin/bash

echo "🚀 Starting No-Code/Low-Code Workflow Builder"
echo "=============================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

echo "📦 Starting services with Docker Compose..."

# Start all services
docker-compose up -d

echo "⏳ Waiting for services to be ready..."

# Wait for PostgreSQL to be ready
echo "🐘 Waiting for PostgreSQL..."
until docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; do
    sleep 2
done

# Wait for backend to be ready
echo "🔧 Waiting for backend API..."
until curl -s http://localhost:8000/health > /dev/null 2>&1; do
    sleep 2
done

# Wait for frontend to be ready
echo "🌐 Waiting for frontend..."
until curl -s http://localhost:3000 > /dev/null 2>&1; do
    sleep 2
done

echo ""
echo "✅ All services are ready!"
echo ""
echo "🌐 Frontend:     http://localhost:3000"
echo "🔧 Backend API:  http://localhost:8000"
echo "📚 API Docs:     http://localhost:8000/docs"
echo "🐘 PostgreSQL:   localhost:5432"
echo "🔍 ChromaDB:     localhost:8001"
echo ""
echo "📋 To view logs: docker-compose logs -f"
echo "🛑 To stop:      docker-compose down"
echo ""

# Run basic test
if command -v python3 &> /dev/null; then
    echo "🧪 Running basic API test..."
    python3 test_backend.py
else
    echo "⚠️  Python3 not found. Skipping API test."
fi

echo ""
echo "🎉 Setup complete! You can now access the application."
