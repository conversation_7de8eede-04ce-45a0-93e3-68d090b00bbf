import React, { useState, useEffect } from 'react';
import { WorkflowComponent } from '../../types';

interface ConfigurationPanelProps {
  component: WorkflowComponent;
  onConfigurationChange: (config: Record<string, any>) => void;
}

const ConfigurationPanel: React.FC<ConfigurationPanelProps> = ({
  component,
  onConfigurationChange,
}) => {
  const [config, setConfig] = useState(component.configuration || {});

  useEffect(() => {
    setConfig(component.configuration || {});
  }, [component]);

  const handleConfigChange = (key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    onConfigurationChange(newConfig);
  };

  const renderUserQueryConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Placeholder Text
        </label>
        <input
          type="text"
          value={config.placeholder || ''}
          onChange={(e) => handleConfigChange('placeholder', e.target.value)}
          placeholder="Enter your question..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Validation
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.validation?.required || false}
              onChange={(e) => handleConfigChange('validation', {
                ...config.validation,
                required: e.target.checked
              })}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="ml-2 text-sm text-gray-600">Required</span>
          </label>
          
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Min Length</label>
              <input
                type="number"
                value={config.validation?.minLength || ''}
                onChange={(e) => handleConfigChange('validation', {
                  ...config.validation,
                  minLength: parseInt(e.target.value) || 0
                })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Max Length</label>
              <input
                type="number"
                value={config.validation?.maxLength || ''}
                onChange={(e) => handleConfigChange('validation', {
                  ...config.validation,
                  maxLength: parseInt(e.target.value) || 1000
                })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderKnowledgeBaseConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Number of Results (Top K)
        </label>
        <input
          type="number"
          value={config.top_k || 5}
          onChange={(e) => handleConfigChange('top_k', parseInt(e.target.value) || 5)}
          min="1"
          max="20"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
        <p className="text-xs text-gray-500 mt-1">
          Number of most relevant document chunks to retrieve
        </p>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Similarity Threshold
        </label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={config.similarity_threshold || 0.7}
          onChange={(e) => handleConfigChange('similarity_threshold', parseFloat(e.target.value))}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-gray-500">
          <span>0.0 (Less strict)</span>
          <span className="font-medium">{config.similarity_threshold || 0.7}</span>
          <span>1.0 (More strict)</span>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          Minimum similarity score for including results
        </p>
      </div>
    </div>
  );

  const renderLLMEngineConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Model
        </label>
        <select
          value={config.model || 'gpt-3.5-turbo'}
          onChange={(e) => handleConfigChange('model', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
          <option value="gpt-4">GPT-4</option>
          <option value="gpt-4-turbo-preview">GPT-4 Turbo</option>
          <option value="gemini-pro">Gemini Pro</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Max Tokens
        </label>
        <input
          type="number"
          value={config.max_tokens || 1000}
          onChange={(e) => handleConfigChange('max_tokens', parseInt(e.target.value) || 1000)}
          min="1"
          max="4000"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Temperature
        </label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={config.temperature || 0.7}
          onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-gray-500">
          <span>0.0 (Focused)</span>
          <span className="font-medium">{config.temperature || 0.7}</span>
          <span>2.0 (Creative)</span>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Custom Prompt
        </label>
        <textarea
          value={config.custom_prompt || ''}
          onChange={(e) => handleConfigChange('custom_prompt', e.target.value)}
          placeholder="Use {query} to reference the user's question..."
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
        <p className="text-xs text-gray-500 mt-1">
          Optional: Customize the prompt sent to the LLM
        </p>
      </div>
      
      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.use_web_search || false}
            onChange={(e) => handleConfigChange('use_web_search', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
          <span className="ml-2 text-sm text-gray-700">Enable Web Search</span>
        </label>
        <p className="text-xs text-gray-500 mt-1">
          Include web search results in the context
        </p>
      </div>
    </div>
  );

  const renderOutputConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Output Format
        </label>
        <select
          value={config.format || 'text'}
          onChange={(e) => handleConfigChange('format', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="text">Plain Text</option>
          <option value="markdown">Markdown</option>
          <option value="json">JSON</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Response Template
        </label>
        <textarea
          value={config.template || ''}
          onChange={(e) => handleConfigChange('template', e.target.value)}
          placeholder="Optional: Format the response using {response} placeholder..."
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
      </div>
    </div>
  );

  const renderConfiguration = () => {
    switch (component.component_type) {
      case 'user_query':
        return renderUserQueryConfig();
      case 'knowledge_base':
        return renderKnowledgeBaseConfig();
      case 'llm_engine':
        return renderLLMEngineConfig();
      case 'output':
        return renderOutputConfig();
      default:
        return (
          <div className="text-center text-gray-500 py-8">
            No configuration options available for this component type.
          </div>
        );
    }
  };

  return (
    <div className="p-4">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{component.name}</h3>
        <p className="text-sm text-gray-600 capitalize">
          {component.component_type.replace('_', ' ')} Component
        </p>
      </div>
      
      {renderConfiguration()}
    </div>
  );
};

export default ConfigurationPanel;
