import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [workflows, setWorkflows] = useState([]);
  const [currentWorkflow, setCurrentWorkflow] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [backendStatus, setBackendStatus] = useState('checking');
  const [chatMessage, setChatMessage] = useState('');
  const [chatResponse, setChatResponse] = useState('');
  const [isChatting, setIsChatting] = useState(false);

  // Check backend health
  useEffect(() => {
    fetch('http://localhost:8000/health')
      .then(res => res.json())
      .then(data => {
        setBackendStatus('connected');
        loadWorkflows();
      })
      .catch(() => {
        setBackendStatus('disconnected');
        setIsLoading(false);
      });
  }, []);

  const loadWorkflows = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/workflows/');
      const data = await response.json();
      setWorkflows(data);
    } catch (error) {
      console.error('Failed to load workflows:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createWorkflow = async () => {
    const name = prompt('Enter workflow name:');
    if (!name) return;

    try {
      const response = await fetch('http://localhost:8000/api/workflows/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, description: 'Created from demo' })
      });
      const newWorkflow = await response.json();
      setWorkflows([...workflows, newWorkflow]);
      setCurrentWorkflow(newWorkflow);
    } catch (error) {
      alert('Failed to create workflow');
    }
  };

  const testChat = async () => {
    if (!currentWorkflow || !chatMessage.trim()) return;

    setIsChatting(true);
    try {
      const response = await fetch('http://localhost:8000/api/chat/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          workflow_id: currentWorkflow.id,
          message: chatMessage
        })
      });
      const data = await response.json();
      setChatResponse(data.response);
    } catch (error) {
      setChatResponse('Error: Failed to get response');
    } finally {
      setIsChatting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h1 className="text-xl font-bold text-gray-900">No-Code Workflow Builder</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                backendStatus === 'connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {backendStatus === 'connected' ? '✅ Backend Connected' : '❌ Backend Disconnected'}
              </div>
              <button
                onClick={createWorkflow}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Create Workflow
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Workflows Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Workflows</h2>
            {workflows.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">No workflows created yet</p>
                <button
                  onClick={createWorkflow}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Create Your First Workflow
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                {workflows.map(workflow => (
                  <div
                    key={workflow.id}
                    onClick={() => setCurrentWorkflow(workflow)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      currentWorkflow?.id === workflow.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <h3 className="font-medium text-gray-900">{workflow.name}</h3>
                    <p className="text-sm text-gray-600">{workflow.description}</p>
                    <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                      <span>Components: {workflow.components.length}</span>
                      <span>Connections: {workflow.connections.length}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Chat Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Chat</h2>
            {currentWorkflow ? (
              <div className="space-y-4">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">Testing workflow: <strong>{currentWorkflow.name}</strong></p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Your Message</label>
                  <textarea
                    value={chatMessage}
                    onChange={(e) => setChatMessage(e.target.value)}
                    placeholder="Type your message here..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                  />
                </div>
                <button
                  onClick={testChat}
                  disabled={!chatMessage.trim() || isChatting}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  {isChatting ? 'Processing...' : 'Send Message'}
                </button>
                {chatResponse && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">AI Response:</h4>
                    <p className="text-gray-900">{chatResponse}</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">Select a workflow to test chat functionality</p>
              </div>
            )}
          </div>
        </div>

        {/* Status Panel */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">System Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl mb-2">🌐</div>
              <h3 className="font-medium text-green-800">Frontend</h3>
              <p className="text-sm text-green-600">Running on port 3000</p>
            </div>
            <div className={`text-center p-4 rounded-lg ${
              backendStatus === 'connected' ? 'bg-green-50' : 'bg-red-50'
            }`}>
              <div className="text-2xl mb-2">{backendStatus === 'connected' ? '🔧' : '❌'}</div>
              <h3 className={`font-medium ${backendStatus === 'connected' ? 'text-green-800' : 'text-red-800'}`}>
                Backend API
              </h3>
              <p className={`text-sm ${backendStatus === 'connected' ? 'text-green-600' : 'text-red-600'}`}>
                {backendStatus === 'connected' ? 'Running on port 8000' : 'Not connected'}
              </p>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl mb-2">🤖</div>
              <h3 className="font-medium text-blue-800">OpenAI</h3>
              <p className="text-sm text-blue-600">API Key Configured</p>
            </div>
          </div>
          <div className="mt-4 text-center">
            <a
              href="http://localhost:8000/docs"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              📚 View API Documentation
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
