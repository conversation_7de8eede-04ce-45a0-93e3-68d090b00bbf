This is a test document for the No-Code/Low-Code Workflow Builder.

The application allows users to create AI-powered workflows by connecting different components:

1. User Query Component - Accepts user input
2. Knowledge Base Component - Searches through documents
3. LLM Engine Component - Processes queries with AI
4. Output Component - Displays results

Key features include:
- Visual workflow builder with React Flow
- Document processing with vector embeddings
- Multiple AI model support (OpenAI GPT, Gemini)
- Real-time chat interface
- Production-ready deployment

This document can be used to test the knowledge base functionality and vector search capabilities.
