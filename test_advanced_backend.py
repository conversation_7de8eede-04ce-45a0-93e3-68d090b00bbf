#!/usr/bin/env python3
"""
Advanced backend testing for document upload and chat functionality
"""

import requests
import json
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_document_upload():
    """Test document upload and processing"""
    print("🔍 Testing document upload...")
    try:
        # Create test file if it doesn't exist
        test_file_path = Path("test_document.txt")
        if not test_file_path.exists():
            with open(test_file_path, "w") as f:
                f.write("""This is a test document for the No-Code/Low-Code Workflow Builder.

The application allows users to create AI-powered workflows by connecting different components:

1. User Query Component - Accepts user input and validates it
2. Knowledge Base Component - Searches through uploaded documents using vector similarity
3. LLM Engine Component - Processes queries using AI models like GPT-3.5, GPT-4, or Gemini
4. Output Component - Displays the final response in a chat interface

Key features include:
- Visual workflow builder with React Flow for drag-and-drop functionality
- Document processing with PyMuPDF for PDF text extraction
- Vector embeddings using OpenAI's text-embedding-ada-002 model
- ChromaDB for efficient vector storage and similarity search
- Multiple AI model support (OpenAI GPT models and Google Gemini)
- Real-time chat interface for testing workflows
- Production-ready deployment with Docker and Kubernetes
- Monitoring with Prometheus and Grafana
- Logging with ELK stack

This document can be used to test the knowledge base functionality and vector search capabilities.
The system should be able to answer questions about workflow components, AI models, and deployment options.""")
        
        # Test document upload
        with open(test_file_path, "rb") as f:
            files = {"file": ("test_document.txt", f, "text/plain")}
            response = requests.post(f"{BASE_URL}/api/documents/upload", files=files)
        
        print(f"Upload status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {result}")
            document_id = result.get("document_id")
            
            # Wait for processing
            print("⏳ Waiting for document processing...")
            time.sleep(5)
            
            # Test get documents
            response = requests.get(f"{BASE_URL}/api/documents/")
            print(f"GET documents status: {response.status_code}")
            if response.status_code == 200:
                documents = response.json()
                print(f"Documents count: {len(documents)}")
                for doc in documents:
                    print(f"  - {doc['filename']} (ID: {doc['id']}, Chunks: {doc.get('chunk_count', 0)})")
            
            print("✅ Document upload tests passed!")
            return document_id
        else:
            print(f"❌ Upload failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Document upload test failed: {e}")
        return None

def test_document_search():
    """Test document search functionality"""
    print("\n🔍 Testing document search...")
    try:
        # Test search with different queries
        test_queries = [
            "workflow components",
            "AI models",
            "deployment",
            "React Flow"
        ]
        
        for query in test_queries:
            print(f"\n🔎 Searching for: '{query}'")
            params = {
                "query": query,
                "top_k": 3,
                "similarity_threshold": 0.5
            }
            response = requests.post(f"{BASE_URL}/api/documents/search", params=params)
            print(f"Search status: {response.status_code}")
            
            if response.status_code == 200:
                results = response.json()
                print(f"Found {len(results)} results:")
                for i, result in enumerate(results, 1):
                    print(f"  {i}. Similarity: {result['similarity']:.3f}")
                    print(f"     Content: {result['content'][:100]}...")
            else:
                print(f"Search failed: {response.text}")
        
        print("✅ Document search tests completed!")
        return True
    except Exception as e:
        print(f"❌ Document search test failed: {e}")
        return False

def test_complete_workflow():
    """Test a complete workflow with all components"""
    print("\n🔍 Testing complete workflow...")
    try:
        # Create workflow
        workflow_data = {
            "name": "Complete Test Workflow",
            "description": "A complete workflow with all components for testing"
        }
        response = requests.post(f"{BASE_URL}/api/workflows/", json=workflow_data)
        if response.status_code != 200:
            print(f"❌ Failed to create workflow: {response.text}")
            return False
        
        workflow = response.json()
        workflow_id = workflow["id"]
        print(f"✅ Created workflow ID: {workflow_id}")
        
        # Add User Query component
        component_data = {
            "component_type": "user_query",
            "component_id": "user_query_1",
            "name": "User Query",
            "position_x": 100,
            "position_y": 100,
            "configuration": {
                "placeholder": "Ask about the workflow builder...",
                "validation": {
                    "required": True,
                    "minLength": 1,
                    "maxLength": 1000
                }
            }
        }
        response = requests.post(f"{BASE_URL}/api/workflows/{workflow_id}/components", json=component_data)
        if response.status_code != 200:
            print(f"❌ Failed to add User Query component: {response.text}")
            return False
        user_query_comp = response.json()
        print(f"✅ Added User Query component (ID: {user_query_comp['id']})")
        
        # Add Knowledge Base component
        kb_component_data = {
            "component_type": "knowledge_base",
            "component_id": "knowledge_base_1",
            "name": "Knowledge Base",
            "position_x": 400,
            "position_y": 100,
            "configuration": {
                "top_k": 5,
                "similarity_threshold": 0.7,
                "document_ids": []
            }
        }
        response = requests.post(f"{BASE_URL}/api/workflows/{workflow_id}/components", json=kb_component_data)
        if response.status_code != 200:
            print(f"❌ Failed to add Knowledge Base component: {response.text}")
            return False
        kb_comp = response.json()
        print(f"✅ Added Knowledge Base component (ID: {kb_comp['id']})")
        
        # Add LLM Engine component
        llm_component_data = {
            "component_type": "llm_engine",
            "component_id": "llm_engine_1",
            "name": "LLM Engine",
            "position_x": 700,
            "position_y": 100,
            "configuration": {
                "model": "gpt-3.5-turbo",
                "max_tokens": 1000,
                "temperature": 0.7,
                "custom_prompt": "You are a helpful assistant for the No-Code/Low-Code Workflow Builder. Use the provided context to answer questions accurately.",
                "use_web_search": False
            }
        }
        response = requests.post(f"{BASE_URL}/api/workflows/{workflow_id}/components", json=llm_component_data)
        if response.status_code != 200:
            print(f"❌ Failed to add LLM Engine component: {response.text}")
            return False
        llm_comp = response.json()
        print(f"✅ Added LLM Engine component (ID: {llm_comp['id']})")
        
        # Add Output component
        output_component_data = {
            "component_type": "output",
            "component_id": "output_1",
            "name": "Output",
            "position_x": 1000,
            "position_y": 100,
            "configuration": {
                "format": "text",
                "template": ""
            }
        }
        response = requests.post(f"{BASE_URL}/api/workflows/{workflow_id}/components", json=output_component_data)
        if response.status_code != 200:
            print(f"❌ Failed to add Output component: {response.text}")
            return False
        output_comp = response.json()
        print(f"✅ Added Output component (ID: {output_comp['id']})")
        
        print("✅ Complete workflow created successfully!")
        return workflow_id
    except Exception as e:
        print(f"❌ Complete workflow test failed: {e}")
        return False

def test_chat_workflow(workflow_id):
    """Test workflow execution via chat"""
    print(f"\n🔍 Testing chat with workflow {workflow_id}...")
    try:
        test_messages = [
            "What is the No-Code/Low-Code Workflow Builder?",
            "What components are available?",
            "How does the Knowledge Base component work?",
            "What AI models are supported?"
        ]
        
        for message in test_messages:
            print(f"\n💬 Testing message: '{message}'")
            chat_data = {
                "workflow_id": workflow_id,
                "message": message
            }
            response = requests.post(f"{BASE_URL}/api/chat/", json=chat_data)
            print(f"Chat status: {response.status_code}")
            
            if response.status_code == 200:
                chat_response = response.json()
                print(f"Session ID: {chat_response.get('session_id')}")
                print(f"Execution time: {chat_response.get('execution_time', 0):.2f}s")
                print(f"Response: {chat_response.get('response', 'No response')[:200]}...")
                
                component_results = chat_response.get('component_results', {})
                if component_results:
                    print(f"Component results: {component_results}")
            else:
                print(f"Chat failed: {response.text}")
            
            # Wait between requests
            time.sleep(1)
        
        print("✅ Chat workflow tests completed!")
        return True
    except Exception as e:
        print(f"❌ Chat workflow test failed: {e}")
        return False

def main():
    """Run all advanced backend tests"""
    print("🚀 Starting advanced backend testing...\n")
    
    # Test 1: Document Upload
    document_id = test_document_upload()
    
    # Test 2: Document Search (if upload succeeded)
    if document_id:
        test_document_search()
    
    # Test 3: Complete Workflow Creation
    workflow_id = test_complete_workflow()
    
    # Test 4: Chat Workflow Execution (if workflow created)
    if workflow_id:
        test_chat_workflow(workflow_id)
    
    print("\n📊 Advanced backend testing completed!")
    print("🎉 Check the results above for any issues.")

if __name__ == "__main__":
    main()
