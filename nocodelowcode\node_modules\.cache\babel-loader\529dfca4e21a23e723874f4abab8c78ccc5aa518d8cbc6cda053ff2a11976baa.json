{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\noCodeLowCode\\\\nocodelowcode\\\\src\\\\components\\\\layout\\\\MainLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Header from './Header';\nimport ComponentPanel from '../workflow/ComponentPanel';\nimport ReactFlowWorkspace from '../workflow/ReactFlowWorkspace';\nimport ConfigurationPanel from '../workflow/ConfigurationPanel';\nimport ChatInterface from '../chat/ChatInterface';\nimport DocumentUpload from '../common/DocumentUpload';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MainLayout = ({\n  workflows,\n  currentWorkflow,\n  onWorkflowSelect,\n  onWorkflowCreate,\n  onWorkflowUpdate\n}) => {\n  _s();\n  var _currentWorkflow$comp;\n  const [selectedComponentId, setSelectedComponentId] = useState(null);\n  const [isChatOpen, setIsChatOpen] = useState(false);\n  const [isConfigPanelOpen, setIsConfigPanelOpen] = useState(false);\n  const selectedComponent = currentWorkflow === null || currentWorkflow === void 0 ? void 0 : (_currentWorkflow$comp = currentWorkflow.components) === null || _currentWorkflow$comp === void 0 ? void 0 : _currentWorkflow$comp.find(comp => comp.component_id === selectedComponentId);\n  const handleComponentSelect = componentId => {\n    setSelectedComponentId(componentId);\n    setIsConfigPanelOpen(!!componentId);\n  };\n  const handleConfigurationChange = config => {\n    if (selectedComponent && currentWorkflow) {\n      const updatedComponents = currentWorkflow.components.map(comp => comp.component_id === selectedComponent.component_id ? {\n        ...comp,\n        configuration: config\n      } : comp);\n      const updatedWorkflow = {\n        ...currentWorkflow,\n        components: updatedComponents\n      };\n      onWorkflowUpdate(updatedWorkflow);\n    }\n  };\n  const handleBuildStack = () => {\n    var _currentWorkflow$comp2, _currentWorkflow$comp3;\n    if (!currentWorkflow) {\n      alert('Please select a workflow first');\n      return;\n    }\n\n    // Validate workflow before enabling chat\n    const hasUserQuery = (_currentWorkflow$comp2 = currentWorkflow.components) === null || _currentWorkflow$comp2 === void 0 ? void 0 : _currentWorkflow$comp2.some(comp => comp.component_type === 'user_query');\n    const hasOutput = (_currentWorkflow$comp3 = currentWorkflow.components) === null || _currentWorkflow$comp3 === void 0 ? void 0 : _currentWorkflow$comp3.some(comp => comp.component_type === 'output');\n    if (!hasUserQuery || !hasOutput) {\n      alert('Workflow must have at least a User Query and Output component');\n      return;\n    }\n    alert('Workflow validated successfully! You can now chat with your stack.');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex flex-col bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      workflows: workflows,\n      currentWorkflow: currentWorkflow,\n      onWorkflowSelect: onWorkflowSelect,\n      onWorkflowCreate: onWorkflowCreate,\n      onBuildStack: handleBuildStack,\n      onChatWithStack: () => setIsChatOpen(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-80 bg-white border-r border-gray-200 flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 border-b border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Components\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mt-1\",\n            children: \"Drag components to the workspace\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(ComponentPanel, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-semibold text-gray-900 mb-3\",\n              children: \"Documents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(DocumentUpload, {\n              onUploadComplete: documentId => {\n                console.log('Document uploaded:', documentId);\n                alert('Document uploaded successfully! You can now use it in Knowledge Base components.');\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col\",\n        children: currentWorkflow ? /*#__PURE__*/_jsxDEV(WorkflowWorkspace, {\n          workflow: currentWorkflow,\n          onWorkflowChange: onWorkflowUpdate,\n          onComponentSelect: handleComponentSelect,\n          selectedComponentId: selectedComponentId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-12 h-12 text-gray-400\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-2\",\n              children: \"No Workflow Selected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4\",\n              children: \"Create a new workflow or select an existing one to get started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onWorkflowCreate,\n              className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: \"Create New Workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), isConfigPanelOpen && selectedComponent && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-80 bg-white border-l border-gray-200 flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 border-b border-gray-200 flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsConfigPanelOpen(false),\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(ConfigurationPanel, {\n            component: selectedComponent,\n            onConfigurationChange: handleConfigurationChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), isChatOpen && currentWorkflow && /*#__PURE__*/_jsxDEV(ChatInterface, {\n      workflow: currentWorkflow,\n      onClose: () => setIsChatOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"VJP2uWe34kDoIOzOEht/Dq/pohY=\");\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useState", "Header", "ComponentPanel", "ReactFlowWorkspace", "ConfigurationPanel", "ChatInterface", "DocumentUpload", "jsxDEV", "_jsxDEV", "MainLayout", "workflows", "currentWorkflow", "onWorkflowSelect", "onWorkflowCreate", "onWorkflowUpdate", "_s", "_currentWorkflow$comp", "selectedComponentId", "setSelectedComponentId", "isChatOpen", "setIsChatOpen", "isConfigPanelOpen", "setIsConfigPanelOpen", "selectedComponent", "components", "find", "comp", "component_id", "handleComponentSelect", "componentId", "handleConfigurationChange", "config", "updatedComponents", "map", "configuration", "updatedWorkflow", "handleBuildStack", "_currentWorkflow$comp2", "_currentWorkflow$comp3", "alert", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "component_type", "hasOutput", "className", "children", "onBuildStack", "onChatWithStack", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onUploadComplete", "documentId", "console", "log", "WorkflowWorkspace", "workflow", "onWorkflowChange", "onComponentSelect", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "fillRule", "clipRule", "component", "onConfigurationChange", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/noCodeLowCode/nocodelowcode/src/components/layout/MainLayout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport Header from './Header';\nimport ComponentPanel from '../workflow/ComponentPanel';\nimport ReactFlowWorkspace from '../workflow/ReactFlowWorkspace';\nimport ConfigurationPanel from '../workflow/ConfigurationPanel';\nimport ChatInterface from '../chat/ChatInterface';\nimport DocumentUpload from '../common/DocumentUpload';\n\nconst MainLayout = ({\n  workflows,\n  currentWorkflow,\n  onWorkflowSelect,\n  onWorkflowCreate,\n  onWorkflowUpdate,\n}) => {\n  const [selectedComponentId, setSelectedComponentId] = useState(null);\n  const [isChatOpen, setIsChatOpen] = useState(false);\n  const [isConfigPanelOpen, setIsConfigPanelOpen] = useState(false);\n\n  const selectedComponent = currentWorkflow?.components?.find(\n    (comp) => comp.component_id === selectedComponentId\n  );\n\n  const handleComponentSelect = (componentId) => {\n    setSelectedComponentId(componentId);\n    setIsConfigPanelOpen(!!componentId);\n  };\n\n  const handleConfigurationChange = (config) => {\n    if (selectedComponent && currentWorkflow) {\n      const updatedComponents = currentWorkflow.components.map((comp) =>\n        comp.component_id === selectedComponent.component_id\n          ? { ...comp, configuration: config }\n          : comp\n      );\n\n      const updatedWorkflow = {\n        ...currentWorkflow,\n        components: updatedComponents,\n      };\n\n      onWorkflowUpdate(updatedWorkflow);\n    }\n  };\n\n  const handleBuildStack = () => {\n    if (!currentWorkflow) {\n      alert('Please select a workflow first');\n      return;\n    }\n    \n    // Validate workflow before enabling chat\n    const hasUserQuery = currentWorkflow.components?.some(\n      (comp) => comp.component_type === 'user_query'\n    );\n    const hasOutput = currentWorkflow.components?.some(\n      (comp) => comp.component_type === 'output'\n    );\n\n    if (!hasUserQuery || !hasOutput) {\n      alert('Workflow must have at least a User Query and Output component');\n      return;\n    }\n\n    alert('Workflow validated successfully! You can now chat with your stack.');\n  };\n\n  return (\n    <div className=\"h-screen flex flex-col bg-gray-50\">\n      <Header\n        workflows={workflows}\n        currentWorkflow={currentWorkflow}\n        onWorkflowSelect={onWorkflowSelect}\n        onWorkflowCreate={onWorkflowCreate}\n        onBuildStack={handleBuildStack}\n        onChatWithStack={() => setIsChatOpen(true)}\n      />\n\n      <div className=\"flex-1 flex overflow-hidden\">\n        {/* Component Library Panel */}\n        <div className=\"w-80 bg-white border-r border-gray-200 flex flex-col\">\n          <div className=\"p-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Components</h2>\n            <p className=\"text-sm text-gray-600 mt-1\">\n              Drag components to the workspace\n            </p>\n          </div>\n          <div className=\"flex-1 overflow-y-auto\">\n            <ComponentPanel />\n\n            {/* Document Upload Section */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <h3 className=\"text-sm font-semibold text-gray-900 mb-3\">Documents</h3>\n              <DocumentUpload\n                onUploadComplete={(documentId) => {\n                  console.log('Document uploaded:', documentId);\n                  alert('Document uploaded successfully! You can now use it in Knowledge Base components.');\n                }}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Main Workspace */}\n        <div className=\"flex-1 flex flex-col\">\n          {currentWorkflow ? (\n            <WorkflowWorkspace\n              workflow={currentWorkflow}\n              onWorkflowChange={onWorkflowUpdate}\n              onComponentSelect={handleComponentSelect}\n              selectedComponentId={selectedComponentId}\n            />\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\">\n                  <svg\n                    className=\"w-12 h-12 text-gray-400\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                    />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  No Workflow Selected\n                </h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Create a new workflow or select an existing one to get started\n                </p>\n                <button\n                  onClick={onWorkflowCreate}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                >\n                  Create New Workflow\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Configuration Panel */}\n        {isConfigPanelOpen && selectedComponent && (\n          <div className=\"w-80 bg-white border-l border-gray-200 flex flex-col\">\n            <div className=\"p-4 border-b border-gray-200 flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">\n                Configuration\n              </h2>\n              <button\n                onClick={() => setIsConfigPanelOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n              </button>\n            </div>\n            <div className=\"flex-1 overflow-y-auto\">\n              <ConfigurationPanel\n                component={selectedComponent}\n                onConfigurationChange={handleConfigurationChange}\n              />\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Chat Interface Modal */}\n      {isChatOpen && currentWorkflow && (\n        <ChatInterface\n          workflow={currentWorkflow}\n          onClose={() => setIsChatOpen(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,UAAU,GAAGA,CAAC;EAClBC,SAAS;EACTC,eAAe;EACfC,gBAAgB;EAChBC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACJ,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMuB,iBAAiB,GAAGZ,eAAe,aAAfA,eAAe,wBAAAK,qBAAA,GAAfL,eAAe,CAAEa,UAAU,cAAAR,qBAAA,uBAA3BA,qBAAA,CAA6BS,IAAI,CACxDC,IAAI,IAAKA,IAAI,CAACC,YAAY,KAAKV,mBAClC,CAAC;EAED,MAAMW,qBAAqB,GAAIC,WAAW,IAAK;IAC7CX,sBAAsB,CAACW,WAAW,CAAC;IACnCP,oBAAoB,CAAC,CAAC,CAACO,WAAW,CAAC;EACrC,CAAC;EAED,MAAMC,yBAAyB,GAAIC,MAAM,IAAK;IAC5C,IAAIR,iBAAiB,IAAIZ,eAAe,EAAE;MACxC,MAAMqB,iBAAiB,GAAGrB,eAAe,CAACa,UAAU,CAACS,GAAG,CAAEP,IAAI,IAC5DA,IAAI,CAACC,YAAY,KAAKJ,iBAAiB,CAACI,YAAY,GAChD;QAAE,GAAGD,IAAI;QAAEQ,aAAa,EAAEH;MAAO,CAAC,GAClCL,IACN,CAAC;MAED,MAAMS,eAAe,GAAG;QACtB,GAAGxB,eAAe;QAClBa,UAAU,EAAEQ;MACd,CAAC;MAEDlB,gBAAgB,CAACqB,eAAe,CAAC;IACnC;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA,EAAAC,sBAAA;IAC7B,IAAI,CAAC3B,eAAe,EAAE;MACpB4B,KAAK,CAAC,gCAAgC,CAAC;MACvC;IACF;;IAEA;IACA,MAAMC,YAAY,IAAAH,sBAAA,GAAG1B,eAAe,CAACa,UAAU,cAAAa,sBAAA,uBAA1BA,sBAAA,CAA4BI,IAAI,CAClDf,IAAI,IAAKA,IAAI,CAACgB,cAAc,KAAK,YACpC,CAAC;IACD,MAAMC,SAAS,IAAAL,sBAAA,GAAG3B,eAAe,CAACa,UAAU,cAAAc,sBAAA,uBAA1BA,sBAAA,CAA4BG,IAAI,CAC/Cf,IAAI,IAAKA,IAAI,CAACgB,cAAc,KAAK,QACpC,CAAC;IAED,IAAI,CAACF,YAAY,IAAI,CAACG,SAAS,EAAE;MAC/BJ,KAAK,CAAC,+DAA+D,CAAC;MACtE;IACF;IAEAA,KAAK,CAAC,oEAAoE,CAAC;EAC7E,CAAC;EAED,oBACE/B,OAAA;IAAKoC,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDrC,OAAA,CAACP,MAAM;MACLS,SAAS,EAAEA,SAAU;MACrBC,eAAe,EAAEA,eAAgB;MACjCC,gBAAgB,EAAEA,gBAAiB;MACnCC,gBAAgB,EAAEA,gBAAiB;MACnCiC,YAAY,EAAEV,gBAAiB;MAC/BW,eAAe,EAAEA,CAAA,KAAM3B,aAAa,CAAC,IAAI;IAAE;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAEF3C,OAAA;MAAKoC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1CrC,OAAA;QAAKoC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnErC,OAAA;UAAKoC,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CrC,OAAA;YAAIoC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE3C,OAAA;YAAGoC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN3C,OAAA;UAAKoC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCrC,OAAA,CAACN,cAAc;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGlB3C,OAAA;YAAKoC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3CrC,OAAA;cAAIoC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvE3C,OAAA,CAACF,cAAc;cACb8C,gBAAgB,EAAGC,UAAU,IAAK;gBAChCC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,UAAU,CAAC;gBAC7Cd,KAAK,CAAC,kFAAkF,CAAC;cAC3F;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3C,OAAA;QAAKoC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAClClC,eAAe,gBACdH,OAAA,CAACgD,iBAAiB;UAChBC,QAAQ,EAAE9C,eAAgB;UAC1B+C,gBAAgB,EAAE5C,gBAAiB;UACnC6C,iBAAiB,EAAE/B,qBAAsB;UACzCX,mBAAmB,EAAEA;QAAoB;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,gBAEF3C,OAAA;UAAKoC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDrC,OAAA;YAAKoC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrC,OAAA;cAAKoC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FrC,OAAA;gBACEoC,SAAS,EAAC,yBAAyB;gBACnCgB,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBAAAjB,QAAA,eAEnBrC,OAAA;kBACEuD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAAwJ;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3J;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3C,OAAA;cAAIoC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAEvD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3C,OAAA;cAAGoC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3C,OAAA;cACE2D,OAAO,EAAEtD,gBAAiB;cAC1B+B,SAAS,EAAC,wNAAwN;cAAAC,QAAA,EACnO;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL9B,iBAAiB,IAAIE,iBAAiB,iBACrCf,OAAA;QAAKoC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnErC,OAAA;UAAKoC,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7ErC,OAAA;YAAIoC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEpD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3C,OAAA;YACE2D,OAAO,EAAEA,CAAA,KAAM7C,oBAAoB,CAAC,KAAK,CAAE;YAC3CsB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7CrC,OAAA;cAAKoC,SAAS,EAAC,SAAS;cAACgB,IAAI,EAAC,cAAc;cAACE,OAAO,EAAC,WAAW;cAAAjB,QAAA,eAC9DrC,OAAA;gBACE4D,QAAQ,EAAC,SAAS;gBAClBF,CAAC,EAAC,oMAAoM;gBACtMG,QAAQ,EAAC;cAAS;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN3C,OAAA;UAAKoC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCrC,OAAA,CAACJ,kBAAkB;YACjBkE,SAAS,EAAE/C,iBAAkB;YAC7BgD,qBAAqB,EAAEzC;UAA0B;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLhC,UAAU,IAAIR,eAAe,iBAC5BH,OAAA,CAACH,aAAa;MACZoD,QAAQ,EAAE9C,eAAgB;MAC1B6D,OAAO,EAAEA,CAAA,KAAMpD,aAAa,CAAC,KAAK;IAAE;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpC,EAAA,CAlLIN,UAAU;AAAAgE,EAAA,GAAVhE,UAAU;AAoLhB,eAAeA,UAAU;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}