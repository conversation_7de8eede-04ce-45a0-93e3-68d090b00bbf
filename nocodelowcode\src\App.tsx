import React from 'react';
import MainLayout from './components/layout/MainLayout';
import { useWorkflows } from './hooks/useWorkflows';
import { Workflow } from './types';

function App() {
  const {
    workflows,
    currentWorkflow,
    isLoading,
    createWorkflow,
    selectWorkflow,
    setCurrentWorkflow,
  } = useWorkflows();

  const handleWorkflowCreate = async () => {
    const name = prompt('Enter workflow name:');
    if (!name) return;

    const description = prompt('Enter workflow description (optional):');

    try {
      await createWorkflow(name, description || undefined);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  const handleWorkflowSelect = (workflow: Workflow) => {
    selectWorkflow(workflow);
  };

  const handleWorkflowUpdate = (updatedWorkflow: Workflow) => {
    setCurrentWorkflow(updatedWorkflow);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading workflows...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="App">
      <MainLayout
        workflows={workflows}
        currentWorkflow={currentWorkflow}
        onWorkflowSelect={handleWorkflowSelect}
        onWorkflowCreate={handleWorkflowCreate}
        onWorkflowUpdate={handleWorkflowUpdate}
      />
    </div>
  );
}

export default App;
