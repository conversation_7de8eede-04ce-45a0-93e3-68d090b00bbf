{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../packages/minimap/src/types.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,OAAO,CAAC;AACtF,OAAO,KAAK,EAAE,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAEvE,MAAM,MAAM,uBAAuB,CAAC,QAAQ,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,CAAC;AAEvF,MAAM,MAAM,YAAY,CAAC,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,SAAS,CAAC,GAAG;IAC1F,SAAS,CAAC,EAAE,MAAM,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IACvD,eAAe,CAAC,EAAE,MAAM,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IAC7D,aAAa,CAAC,EAAE,MAAM,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IAC3D,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,aAAa,CAAC,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAChD,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,QAAQ,CAAC,EAAE,aAAa,CAAC;IACzB,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,KAAK,IAAI,CAAC;IAC5D,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC;IAChE,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG,IAAI,CAC7B,YAAY,EACZ,WAAW,GAAG,iBAAiB,GAAG,eAAe,GAAG,kBAAkB,GAAG,iBAAiB,GAAG,eAAe,CAC7G,GAAG;IACF,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;CACvD,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;IACd,cAAc,EAAE,MAAM,CAAC;IACvB,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,aAAa,CAAC;IACtB,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,KAAK,IAAI,CAAC;CACnD,CAAC"}