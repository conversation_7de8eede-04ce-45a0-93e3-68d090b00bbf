// TypeScript Version: 3.8

import {getQueriesForElement} from './get-queries-for-element'
import * as queries from './queries'
import * as queryHelpers from './query-helpers'

declare const within: typeof getQueriesForElement
export {queries, queryHelpers, within}

export * from './queries'
export * from './query-helpers'
export * from './screen'
export * from './wait-for'
export * from './wait-for-element-to-be-removed'
export * from './matches'
export * from './get-node-text'
export * from './events'
export * from './get-queries-for-element'
export * from './pretty-dom'
export * from './role-helpers'
export * from './config'
export * from './suggestions'
