import axios from 'axios';
import {
  Workflow,
  WorkflowComponent,
  WorkflowConnection,
  Document,
  ChatRequest,
  ChatResponse,
  DocumentSearchResult,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token if needed
api.interceptors.request.use(
  (config) => {
    // Add auth token here if implementing authentication
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Workflow API
export const workflowApi = {
  // Get all workflows
  getWorkflows: async (): Promise<Workflow[]> => {
    const response = await api.get('/api/workflows/');
    return response.data;
  },

  // Get workflow by ID
  getWorkflow: async (id: number): Promise<Workflow> => {
    const response = await api.get(`/api/workflows/${id}`);
    return response.data;
  },

  // Create new workflow
  createWorkflow: async (workflow: Partial<Workflow>): Promise<Workflow> => {
    const response = await api.post('/api/workflows/', workflow);
    return response.data;
  },

  // Update workflow
  updateWorkflow: async (id: number, workflow: Partial<Workflow>): Promise<Workflow> => {
    const response = await api.put(`/api/workflows/${id}`, workflow);
    return response.data;
  },

  // Delete workflow
  deleteWorkflow: async (id: number): Promise<void> => {
    await api.delete(`/api/workflows/${id}`);
  },

  // Component operations
  createComponent: async (workflowId: number, component: Partial<WorkflowComponent>): Promise<WorkflowComponent> => {
    const response = await api.post(`/api/workflows/${workflowId}/components`, component);
    return response.data;
  },

  updateComponent: async (workflowId: number, componentId: number, component: Partial<WorkflowComponent>): Promise<WorkflowComponent> => {
    const response = await api.put(`/api/workflows/${workflowId}/components/${componentId}`, component);
    return response.data;
  },

  deleteComponent: async (workflowId: number, componentId: number): Promise<void> => {
    await api.delete(`/api/workflows/${workflowId}/components/${componentId}`);
  },

  // Connection operations
  createConnection: async (workflowId: number, connection: Partial<WorkflowConnection>): Promise<WorkflowConnection> => {
    const response = await api.post(`/api/workflows/${workflowId}/connections`, connection);
    return response.data;
  },

  deleteConnection: async (workflowId: number, connectionId: number): Promise<void> => {
    await api.delete(`/api/workflows/${workflowId}/connections/${connectionId}`);
  },
};

// Document API
export const documentApi = {
  // Upload document
  uploadDocument: async (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/api/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get all documents
  getDocuments: async (): Promise<Document[]> => {
    const response = await api.get('/api/documents/');
    return response.data;
  },

  // Get document by ID
  getDocument: async (id: number): Promise<Document> => {
    const response = await api.get(`/api/documents/${id}`);
    return response.data;
  },

  // Delete document
  deleteDocument: async (id: number): Promise<void> => {
    await api.delete(`/api/documents/${id}`);
  },

  // Search documents
  searchDocuments: async (query: string, topK: number = 5, threshold: number = 0.7): Promise<DocumentSearchResult[]> => {
    const response = await api.post('/api/documents/search', {
      query,
      top_k: topK,
      similarity_threshold: threshold,
    });
    return response.data;
  },
};

// Chat API
export const chatApi = {
  // Send chat message
  sendMessage: async (request: ChatRequest): Promise<ChatResponse> => {
    const response = await api.post('/api/chat/', request);
    return response.data;
  },

  // Get chat sessions
  getChatSessions: async (): Promise<any[]> => {
    const response = await api.get('/api/chat/sessions');
    return response.data;
  },

  // Get chat session by ID
  getChatSession: async (sessionId: string): Promise<any> => {
    const response = await api.get(`/api/chat/sessions/${sessionId}`);
    return response.data;
  },

  // Delete chat session
  deleteChatSession: async (sessionId: string): Promise<void> => {
    await api.delete(`/api/chat/sessions/${sessionId}`);
  },
};

// LLM API
export const llmApi = {
  // Generate text
  generateText: async (prompt: string, model: string = 'gpt-3.5-turbo', options: any = {}): Promise<any> => {
    const response = await api.post('/api/llm/generate', {
      prompt,
      model,
      ...options,
    });
    return response.data;
  },

  // Get available models
  getModels: async (): Promise<any> => {
    const response = await api.get('/api/llm/models');
    return response.data;
  },

  // Web search
  webSearch: async (query: string, numResults: number = 5): Promise<any[]> => {
    const response = await api.post('/api/llm/web-search', {
      query,
      num_results: numResults,
    });
    return response.data;
  },

  // Generate embeddings
  generateEmbeddings: async (text: string, model: string = 'text-embedding-ada-002'): Promise<any> => {
    const response = await api.post('/api/llm/embeddings', null, {
      params: { text, model },
    });
    return response.data;
  },
};

export default api;
