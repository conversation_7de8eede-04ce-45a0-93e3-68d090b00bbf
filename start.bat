@echo off
echo 🚀 Starting No-Code/Low-Code Workflow Builder
echo ==============================================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose is not installed. Please install docker-compose first.
    pause
    exit /b 1
)

echo 📦 Starting services with Docker Compose...

REM Start all services
docker-compose up -d

echo ⏳ Waiting for services to be ready...

REM Wait for services (simplified for Windows)
timeout /t 30 /nobreak >nul

echo.
echo ✅ Services should be ready!
echo.
echo 🌐 Frontend:     http://localhost:3000
echo 🔧 Backend API:  http://localhost:8000
echo 📚 API Docs:     http://localhost:8000/docs
echo 🐘 PostgreSQL:   localhost:5432
echo 🔍 ChromaDB:     localhost:8001
echo.
echo 📋 To view logs: docker-compose logs -f
echo 🛑 To stop:      docker-compose down
echo.

REM Run basic test if Python is available
python --version >nul 2>&1
if not errorlevel 1 (
    echo 🧪 Running basic API test...
    python test_backend.py
) else (
    echo ⚠️  Python not found. Skipping API test.
)

echo.
echo 🎉 Setup complete! You can now access the application.
pause
