#!/usr/bin/env python3
"""
Simple frontend and integration testing without Selenium
"""

import time
import requests
import threading
import json

def test_frontend_accessibility():
    """Test if frontend is accessible"""
    print("🔍 Testing frontend accessibility...")
    
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        print(f"Frontend status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            
            # Check if it's a React app
            content = response.text.lower()
            if "react" in content or "root" in content or "div id=\"root\"" in content:
                print("✅ React app detected")
            
            # Check for basic HTML structure
            if "<html" in content and "<body" in content:
                print("✅ Valid HTML structure")
            
            return True
        else:
            print(f"❌ Frontend not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Frontend accessibility test failed: {e}")
        return False

def test_cors_configuration():
    """Test CORS configuration"""
    print("\n🔍 Testing CORS configuration...")
    
    try:
        # Test preflight request
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = requests.options("http://localhost:8000/api/workflows/", headers=headers)
        print(f"CORS preflight status: {response.status_code}")
        
        # Check CORS headers
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        
        print(f"CORS headers: {cors_headers}")
        
        # Test actual API call with origin
        response = requests.get("http://localhost:8000/api/workflows/", 
                              headers={'Origin': 'http://localhost:3000'})
        print(f"API call with origin status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ CORS configuration working")
            return True
        else:
            print(f"❌ CORS configuration failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ CORS test failed: {e}")
        return False

def test_api_endpoints():
    """Test all API endpoints"""
    print("\n🔍 Testing API endpoints...")
    
    endpoints = [
        ("GET", "/health", None),
        ("GET", "/api/workflows/", None),
        ("GET", "/docs", None),
        ("GET", "/openapi.json", None),
        ("POST", "/api/workflows/", {"name": "Test API", "description": "Testing API endpoints"})
    ]
    
    results = []
    
    for method, endpoint, data in endpoints:
        try:
            url = f"http://localhost:8000{endpoint}"
            
            if method == "GET":
                response = requests.get(url)
            elif method == "POST":
                response = requests.post(url, json=data)
            
            print(f"{method} {endpoint}: {response.status_code}")
            
            if response.status_code in [200, 201]:
                results.append(True)
                print(f"   ✅ Success")
            else:
                results.append(False)
                print(f"   ❌ Failed: {response.text[:100]}")
                
        except Exception as e:
            results.append(False)
            print(f"   ❌ Error: {e}")
    
    passed = sum(results)
    total = len(results)
    print(f"\nAPI endpoints: {passed}/{total} working")
    
    return passed >= total * 0.8  # 80% success rate

def test_error_handling():
    """Test error handling"""
    print("\n🔍 Testing error handling...")
    
    error_tests = [
        ("Invalid workflow ID", "GET", "/api/workflows/99999", None, 404),
        ("Invalid endpoint", "GET", "/api/invalid", None, 404),
        ("Empty workflow name", "POST", "/api/workflows/", {"name": "", "description": "test"}, [400, 422]),
        ("Missing data", "POST", "/api/workflows/", {}, [400, 422])
    ]
    
    results = []
    
    for test_name, method, endpoint, data, expected_status in error_tests:
        try:
            url = f"http://localhost:8000{endpoint}"
            
            if method == "GET":
                response = requests.get(url)
            elif method == "POST":
                response = requests.post(url, json=data)
            
            if isinstance(expected_status, list):
                success = response.status_code in expected_status
            else:
                success = response.status_code == expected_status
            
            print(f"{test_name}: {response.status_code} {'✅' if success else '❌'}")
            results.append(success)
            
        except Exception as e:
            print(f"{test_name}: ❌ Error: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    print(f"\nError handling: {passed}/{total} tests passed")
    
    return passed >= total * 0.75  # 75% success rate

def test_performance():
    """Test basic performance"""
    print("\n🔍 Testing performance...")
    
    try:
        # Test frontend load time
        start_time = time.time()
        response = requests.get("http://localhost:3000")
        frontend_time = time.time() - start_time
        print(f"Frontend load time: {frontend_time:.2f}s")
        
        # Test API health check time
        start_time = time.time()
        response = requests.get("http://localhost:8000/health")
        health_time = time.time() - start_time
        print(f"Health check time: {health_time:.2f}s")
        
        # Test workflow creation time
        start_time = time.time()
        response = requests.post("http://localhost:8000/api/workflows/", 
                               json={"name": "Performance Test", "description": "Testing"})
        workflow_time = time.time() - start_time
        print(f"Workflow creation time: {workflow_time:.2f}s")
        
        # Performance evaluation
        performance_score = 0
        
        if frontend_time < 3.0:
            print("✅ Frontend load time good")
            performance_score += 1
        else:
            print("⚠️ Frontend load time slow")
        
        if health_time < 1.0:
            print("✅ API response time good")
            performance_score += 1
        else:
            print("⚠️ API response time slow")
        
        if workflow_time < 2.0:
            print("✅ Workflow creation time good")
            performance_score += 1
        else:
            print("⚠️ Workflow creation time slow")
        
        print(f"Performance score: {performance_score}/3")
        return performance_score >= 2
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def test_concurrent_requests():
    """Test concurrent request handling"""
    print("\n🔍 Testing concurrent requests...")
    
    try:
        results = []
        
        def make_request():
            try:
                response = requests.get("http://localhost:8000/health", timeout=5)
                results.append(response.status_code == 200)
            except:
                results.append(False)
        
        # Create 10 concurrent threads
        threads = []
        for i in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        # Start all threads
        start_time = time.time()
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        successful = sum(results)
        
        print(f"Concurrent requests: {successful}/10 successful in {total_time:.2f}s")
        
        if successful >= 8:
            print("✅ Concurrent request handling good")
            return True
        else:
            print("⚠️ Concurrent request handling needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Concurrent request test failed: {e}")
        return False

def test_full_workflow():
    """Test complete workflow creation and execution"""
    print("\n🔍 Testing complete workflow...")
    
    try:
        # Create workflow
        workflow_data = {"name": "Complete Test Workflow", "description": "Full workflow test"}
        response = requests.post("http://localhost:8000/api/workflows/", json=workflow_data)
        
        if response.status_code != 200:
            print(f"❌ Failed to create workflow: {response.status_code}")
            return False
        
        workflow = response.json()
        workflow_id = workflow["id"]
        print(f"✅ Created workflow ID: {workflow_id}")
        
        # Add components
        components = [
            {
                "component_type": "user_query",
                "component_id": "user_query_1",
                "name": "User Query",
                "position_x": 100,
                "position_y": 100,
                "configuration": {}
            },
            {
                "component_type": "knowledge_base",
                "component_id": "knowledge_base_1",
                "name": "Knowledge Base",
                "position_x": 400,
                "position_y": 100,
                "configuration": {"top_k": 3, "similarity_threshold": 0.7}
            },
            {
                "component_type": "llm_engine",
                "component_id": "llm_engine_1",
                "name": "LLM Engine",
                "position_x": 700,
                "position_y": 100,
                "configuration": {"model": "gpt-3.5-turbo", "temperature": 0.7}
            },
            {
                "component_type": "output",
                "component_id": "output_1",
                "name": "Output",
                "position_x": 1000,
                "position_y": 100,
                "configuration": {}
            }
        ]
        
        for component in components:
            response = requests.post(f"http://localhost:8000/api/workflows/{workflow_id}/components", 
                                   json=component)
            if response.status_code == 200:
                print(f"✅ Added {component['component_type']} component")
            else:
                print(f"❌ Failed to add {component['component_type']} component")
        
        # Test chat
        chat_data = {"workflow_id": workflow_id, "message": "What is this workflow about?"}
        response = requests.post("http://localhost:8000/api/chat/", json=chat_data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Chat test successful (execution time: {result.get('execution_time', 0):.2f}s)")
            return True
        else:
            print(f"❌ Chat test failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Complete workflow test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting comprehensive application testing...\n")
    
    tests = [
        ("Frontend Accessibility", test_frontend_accessibility),
        ("CORS Configuration", test_cors_configuration),
        ("API Endpoints", test_api_endpoints),
        ("Error Handling", test_error_handling),
        ("Performance", test_performance),
        ("Concurrent Requests", test_concurrent_requests),
        ("Complete Workflow", test_full_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append(result)
            print(f"Result: {'✅ PASSED' if result else '❌ FAILED'}")
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n{'='*60}")
    print(f"📊 FINAL TEST RESULTS: {passed}/{total} tests passed")
    print('='*60)
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The application is working perfectly!")
        print("✅ Ready for production deployment!")
    elif passed >= total * 0.8:
        print("🌟 Most tests passed! The application is functional with minor issues.")
        print("💡 Consider addressing the failed tests for optimal performance.")
    else:
        print("⚠️ Several tests failed. The application needs attention.")
        print("🔧 Please review the failed tests and fix the issues.")
    
    print(f"\n🌐 Application URLs:")
    print(f"   Frontend: http://localhost:3000")
    print(f"   Backend API: http://localhost:8000")
    print(f"   API Documentation: http://localhost:8000/docs")
    print(f"   Health Check: http://localhost:8000/health")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    main()
