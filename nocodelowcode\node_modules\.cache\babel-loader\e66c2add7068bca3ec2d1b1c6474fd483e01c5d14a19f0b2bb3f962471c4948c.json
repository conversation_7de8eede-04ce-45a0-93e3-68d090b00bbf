{"ast": null, "code": "/** @license React vundefined\n * react-refresh-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    // ATTENTION\n    // When adding new symbols to this file,\n    // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n    // The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n    // nor polyfill, then a plain number is used for performance.\n    var REACT_ELEMENT_TYPE = 0xeac7;\n    var REACT_PORTAL_TYPE = 0xeaca;\n    var REACT_FRAGMENT_TYPE = 0xeacb;\n    var REACT_STRICT_MODE_TYPE = 0xeacc;\n    var REACT_PROFILER_TYPE = 0xead2;\n    var REACT_PROVIDER_TYPE = 0xeacd;\n    var REACT_CONTEXT_TYPE = 0xeace;\n    var REACT_FORWARD_REF_TYPE = 0xead0;\n    var REACT_SUSPENSE_TYPE = 0xead1;\n    var REACT_SUSPENSE_LIST_TYPE = 0xead8;\n    var REACT_MEMO_TYPE = 0xead3;\n    var REACT_LAZY_TYPE = 0xead4;\n    var REACT_SCOPE_TYPE = 0xead7;\n    var REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\n    var REACT_OFFSCREEN_TYPE = 0xeae2;\n    var REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n    var REACT_CACHE_TYPE = 0xeae4;\n    if (typeof Symbol === 'function' && Symbol.for) {\n      var symbolFor = Symbol.for;\n      REACT_ELEMENT_TYPE = symbolFor('react.element');\n      REACT_PORTAL_TYPE = symbolFor('react.portal');\n      REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n      REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n      REACT_PROFILER_TYPE = symbolFor('react.profiler');\n      REACT_PROVIDER_TYPE = symbolFor('react.provider');\n      REACT_CONTEXT_TYPE = symbolFor('react.context');\n      REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n      REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n      REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n      REACT_MEMO_TYPE = symbolFor('react.memo');\n      REACT_LAZY_TYPE = symbolFor('react.lazy');\n      REACT_SCOPE_TYPE = symbolFor('react.scope');\n      REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n      REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n      REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n      REACT_CACHE_TYPE = symbolFor('react.cache');\n    }\n    var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map; // We never remove these associations.\n    // It's OK to reference families, but use WeakMap/Set for types.\n\n    var allFamiliesByID = new Map();\n    var allFamiliesByType = new PossiblyWeakMap();\n    var allSignaturesByType = new PossiblyWeakMap(); // This WeakMap is read by React, so we only put families\n    // that have actually been edited here. This keeps checks fast.\n    // $FlowIssue\n\n    var updatedFamiliesByType = new PossiblyWeakMap(); // This is cleared on every performReactRefresh() call.\n    // It is an array of [Family, NextType] tuples.\n\n    var pendingUpdates = []; // This is injected by the renderer via DevTools global hook.\n\n    var helpersByRendererID = new Map();\n    var helpersByRoot = new Map(); // We keep track of mounted roots so we can schedule updates.\n\n    var mountedRoots = new Set(); // If a root captures an error, we remember it so we can retry on edit.\n\n    var failedRoots = new Set(); // In environments that support WeakMap, we also remember the last element for every root.\n    // It needs to be weak because we do this even for roots that failed to mount.\n    // If there is no WeakMap, we won't attempt to do retrying.\n    // $FlowIssue\n\n    var rootElements =\n    // $FlowIssue\n    typeof WeakMap === 'function' ? new WeakMap() : null;\n    var isPerformingRefresh = false;\n    function computeFullKey(signature) {\n      if (signature.fullKey !== null) {\n        return signature.fullKey;\n      }\n      var fullKey = signature.ownKey;\n      var hooks;\n      try {\n        hooks = signature.getCustomHooks();\n      } catch (err) {\n        // This can happen in an edge case, e.g. if expression like Foo.useSomething\n        // depends on Foo which is lazily initialized during rendering.\n        // In that case just assume we'll have to remount.\n        signature.forceReset = true;\n        signature.fullKey = fullKey;\n        return fullKey;\n      }\n      for (var i = 0; i < hooks.length; i++) {\n        var hook = hooks[i];\n        if (typeof hook !== 'function') {\n          // Something's wrong. Assume we need to remount.\n          signature.forceReset = true;\n          signature.fullKey = fullKey;\n          return fullKey;\n        }\n        var nestedHookSignature = allSignaturesByType.get(hook);\n        if (nestedHookSignature === undefined) {\n          // No signature means Hook wasn't in the source code, e.g. in a library.\n          // We'll skip it because we can assume it won't change during this session.\n          continue;\n        }\n        var nestedHookKey = computeFullKey(nestedHookSignature);\n        if (nestedHookSignature.forceReset) {\n          signature.forceReset = true;\n        }\n        fullKey += '\\n---\\n' + nestedHookKey;\n      }\n      signature.fullKey = fullKey;\n      return fullKey;\n    }\n    function haveEqualSignatures(prevType, nextType) {\n      var prevSignature = allSignaturesByType.get(prevType);\n      var nextSignature = allSignaturesByType.get(nextType);\n      if (prevSignature === undefined && nextSignature === undefined) {\n        return true;\n      }\n      if (prevSignature === undefined || nextSignature === undefined) {\n        return false;\n      }\n      if (computeFullKey(prevSignature) !== computeFullKey(nextSignature)) {\n        return false;\n      }\n      if (nextSignature.forceReset) {\n        return false;\n      }\n      return true;\n    }\n    function isReactClass(type) {\n      return type.prototype && type.prototype.isReactComponent;\n    }\n    function canPreserveStateBetween(prevType, nextType) {\n      if (isReactClass(prevType) || isReactClass(nextType)) {\n        return false;\n      }\n      if (haveEqualSignatures(prevType, nextType)) {\n        return true;\n      }\n      return false;\n    }\n    function resolveFamily(type) {\n      // Only check updated types to keep lookups fast.\n      return updatedFamiliesByType.get(type);\n    } // If we didn't care about IE11, we could use new Map/Set(iterable).\n\n    function cloneMap(map) {\n      var clone = new Map();\n      map.forEach(function (value, key) {\n        clone.set(key, value);\n      });\n      return clone;\n    }\n    function cloneSet(set) {\n      var clone = new Set();\n      set.forEach(function (value) {\n        clone.add(value);\n      });\n      return clone;\n    } // This is a safety mechanism to protect against rogue getters and Proxies.\n\n    function getProperty(object, property) {\n      try {\n        return object[property];\n      } catch (err) {\n        // Intentionally ignore.\n        return undefined;\n      }\n    }\n    function performReactRefresh() {\n      if (pendingUpdates.length === 0) {\n        return null;\n      }\n      if (isPerformingRefresh) {\n        return null;\n      }\n      isPerformingRefresh = true;\n      try {\n        var staleFamilies = new Set();\n        var updatedFamilies = new Set();\n        var updates = pendingUpdates;\n        pendingUpdates = [];\n        updates.forEach(function (_ref) {\n          var family = _ref[0],\n            nextType = _ref[1];\n          // Now that we got a real edit, we can create associations\n          // that will be read by the React reconciler.\n          var prevType = family.current;\n          updatedFamiliesByType.set(prevType, family);\n          updatedFamiliesByType.set(nextType, family);\n          family.current = nextType; // Determine whether this should be a re-render or a re-mount.\n\n          if (canPreserveStateBetween(prevType, nextType)) {\n            updatedFamilies.add(family);\n          } else {\n            staleFamilies.add(family);\n          }\n        }); // TODO: rename these fields to something more meaningful.\n\n        var update = {\n          updatedFamilies: updatedFamilies,\n          // Families that will re-render preserving state\n          staleFamilies: staleFamilies // Families that will be remounted\n        };\n        helpersByRendererID.forEach(function (helpers) {\n          // Even if there are no roots, set the handler on first update.\n          // This ensures that if *new* roots are mounted, they'll use the resolve handler.\n          helpers.setRefreshHandler(resolveFamily);\n        });\n        var didError = false;\n        var firstError = null; // We snapshot maps and sets that are mutated during commits.\n        // If we don't do this, there is a risk they will be mutated while\n        // we iterate over them. For example, trying to recover a failed root\n        // may cause another root to be added to the failed list -- an infinite loop.\n\n        var failedRootsSnapshot = cloneSet(failedRoots);\n        var mountedRootsSnapshot = cloneSet(mountedRoots);\n        var helpersByRootSnapshot = cloneMap(helpersByRoot);\n        failedRootsSnapshot.forEach(function (root) {\n          var helpers = helpersByRootSnapshot.get(root);\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n          if (!failedRoots.has(root)) {// No longer failed.\n          }\n          if (rootElements === null) {\n            return;\n          }\n          if (!rootElements.has(root)) {\n            return;\n          }\n          var element = rootElements.get(root);\n          try {\n            helpers.scheduleRoot(root, element);\n          } catch (err) {\n            if (!didError) {\n              didError = true;\n              firstError = err;\n            } // Keep trying other roots.\n          }\n        });\n        mountedRootsSnapshot.forEach(function (root) {\n          var helpers = helpersByRootSnapshot.get(root);\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n          if (!mountedRoots.has(root)) {// No longer mounted.\n          }\n          try {\n            helpers.scheduleRefresh(root, update);\n          } catch (err) {\n            if (!didError) {\n              didError = true;\n              firstError = err;\n            } // Keep trying other roots.\n          }\n        });\n        if (didError) {\n          throw firstError;\n        }\n        return update;\n      } finally {\n        isPerformingRefresh = false;\n      }\n    }\n    function register(type, id) {\n      {\n        if (type === null) {\n          return;\n        }\n        if (typeof type !== 'function' && typeof type !== 'object') {\n          return;\n        } // This can happen in an edge case, e.g. if we register\n        // return value of a HOC but it returns a cached component.\n        // Ignore anything but the first registration for each type.\n\n        if (allFamiliesByType.has(type)) {\n          return;\n        } // Create family or remember to update it.\n        // None of this bookkeeping affects reconciliation\n        // until the first performReactRefresh() call above.\n\n        var family = allFamiliesByID.get(id);\n        if (family === undefined) {\n          family = {\n            current: type\n          };\n          allFamiliesByID.set(id, family);\n        } else {\n          pendingUpdates.push([family, type]);\n        }\n        allFamiliesByType.set(type, family); // Visit inner types because we might not have registered them.\n\n        if (typeof type === 'object' && type !== null) {\n          switch (getProperty(type, '$$typeof')) {\n            case REACT_FORWARD_REF_TYPE:\n              register(type.render, id + '$render');\n              break;\n            case REACT_MEMO_TYPE:\n              register(type.type, id + '$type');\n              break;\n          }\n        }\n      }\n    }\n    function setSignature(type, key) {\n      var forceReset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var getCustomHooks = arguments.length > 3 ? arguments[3] : undefined;\n      {\n        if (!allSignaturesByType.has(type)) {\n          allSignaturesByType.set(type, {\n            forceReset: forceReset,\n            ownKey: key,\n            fullKey: null,\n            getCustomHooks: getCustomHooks || function () {\n              return [];\n            }\n          });\n        } // Visit inner types because we might not have signed them.\n\n        if (typeof type === 'object' && type !== null) {\n          switch (getProperty(type, '$$typeof')) {\n            case REACT_FORWARD_REF_TYPE:\n              setSignature(type.render, key, forceReset, getCustomHooks);\n              break;\n            case REACT_MEMO_TYPE:\n              setSignature(type.type, key, forceReset, getCustomHooks);\n              break;\n          }\n        }\n      }\n    } // This is lazily called during first render for a type.\n    // It captures Hook list at that time so inline requires don't break comparisons.\n\n    function collectCustomHooksForSignature(type) {\n      {\n        var signature = allSignaturesByType.get(type);\n        if (signature !== undefined) {\n          computeFullKey(signature);\n        }\n      }\n    }\n    function getFamilyByID(id) {\n      {\n        return allFamiliesByID.get(id);\n      }\n    }\n    function getFamilyByType(type) {\n      {\n        return allFamiliesByType.get(type);\n      }\n    }\n    function findAffectedHostInstances(families) {\n      {\n        var affectedInstances = new Set();\n        mountedRoots.forEach(function (root) {\n          var helpers = helpersByRoot.get(root);\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n          var instancesForRoot = helpers.findHostInstancesForRefresh(root, families);\n          instancesForRoot.forEach(function (inst) {\n            affectedInstances.add(inst);\n          });\n        });\n        return affectedInstances;\n      }\n    }\n    function injectIntoGlobalHook(globalObject) {\n      {\n        // For React Native, the global hook will be set up by require('react-devtools-core').\n        // That code will run before us. So we need to monkeypatch functions on existing hook.\n        // For React Web, the global hook will be set up by the extension.\n        // This will also run before us.\n        var hook = globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__;\n        if (hook === undefined) {\n          // However, if there is no DevTools extension, we'll need to set up the global hook ourselves.\n          // Note that in this case it's important that renderer code runs *after* this method call.\n          // Otherwise, the renderer will think that there is no global hook, and won't do the injection.\n          var nextID = 0;\n          globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__ = hook = {\n            renderers: new Map(),\n            supportsFiber: true,\n            inject: function (injected) {\n              return nextID++;\n            },\n            onScheduleFiberRoot: function (id, root, children) {},\n            onCommitFiberRoot: function (id, root, maybePriorityLevel, didError) {},\n            onCommitFiberUnmount: function () {}\n          };\n        }\n        if (hook.isDisabled) {\n          // This isn't a real property on the hook, but it can be set to opt out\n          // of DevTools integration and associated warnings and logs.\n          // Using console['warn'] to evade Babel and ESLint\n          console['warn']('Something has shimmed the React DevTools global hook (__REACT_DEVTOOLS_GLOBAL_HOOK__). ' + 'Fast Refresh is not compatible with this shim and will be disabled.');\n          return;\n        } // Here, we just want to get a reference to scheduleRefresh.\n\n        var oldInject = hook.inject;\n        hook.inject = function (injected) {\n          var id = oldInject.apply(this, arguments);\n          if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n            // This version supports React Refresh.\n            helpersByRendererID.set(id, injected);\n          }\n          return id;\n        }; // Do the same for any already injected roots.\n        // This is useful if ReactDOM has already been initialized.\n        // https://github.com/facebook/react/issues/17626\n\n        hook.renderers.forEach(function (injected, id) {\n          if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n            // This version supports React Refresh.\n            helpersByRendererID.set(id, injected);\n          }\n        }); // We also want to track currently mounted roots.\n\n        var oldOnCommitFiberRoot = hook.onCommitFiberRoot;\n        var oldOnScheduleFiberRoot = hook.onScheduleFiberRoot || function () {};\n        hook.onScheduleFiberRoot = function (id, root, children) {\n          if (!isPerformingRefresh) {\n            // If it was intentionally scheduled, don't attempt to restore.\n            // This includes intentionally scheduled unmounts.\n            failedRoots.delete(root);\n            if (rootElements !== null) {\n              rootElements.set(root, children);\n            }\n          }\n          return oldOnScheduleFiberRoot.apply(this, arguments);\n        };\n        hook.onCommitFiberRoot = function (id, root, maybePriorityLevel, didError) {\n          var helpers = helpersByRendererID.get(id);\n          if (helpers !== undefined) {\n            helpersByRoot.set(root, helpers);\n            var current = root.current;\n            var alternate = current.alternate; // We need to determine whether this root has just (un)mounted.\n            // This logic is copy-pasted from similar logic in the DevTools backend.\n            // If this breaks with some refactoring, you'll want to update DevTools too.\n\n            if (alternate !== null) {\n              var wasMounted = alternate.memoizedState != null && alternate.memoizedState.element != null;\n              var isMounted = current.memoizedState != null && current.memoizedState.element != null;\n              if (!wasMounted && isMounted) {\n                // Mount a new root.\n                mountedRoots.add(root);\n                failedRoots.delete(root);\n              } else if (wasMounted && isMounted) ;else if (wasMounted && !isMounted) {\n                // Unmount an existing root.\n                mountedRoots.delete(root);\n                if (didError) {\n                  // We'll remount it on future edits.\n                  failedRoots.add(root);\n                } else {\n                  helpersByRoot.delete(root);\n                }\n              } else if (!wasMounted && !isMounted) {\n                if (didError) {\n                  // We'll remount it on future edits.\n                  failedRoots.add(root);\n                }\n              }\n            } else {\n              // Mount a new root.\n              mountedRoots.add(root);\n            }\n          } // Always call the decorated DevTools hook.\n\n          return oldOnCommitFiberRoot.apply(this, arguments);\n        };\n      }\n    }\n    function hasUnrecoverableErrors() {\n      // TODO: delete this after removing dependency in RN.\n      return false;\n    } // Exposed for testing.\n\n    function _getMountedRootCount() {\n      {\n        return mountedRoots.size;\n      }\n    } // This is a wrapper over more primitive functions for setting signature.\n    // Signatures let us decide whether the Hook order has changed on refresh.\n    //\n    // This function is intended to be used as a transform target, e.g.:\n    // var _s = createSignatureFunctionForTransform()\n    //\n    // function Hello() {\n    //   const [foo, setFoo] = useState(0);\n    //   const value = useCustomHook();\n    //   _s(); /* Call without arguments triggers collecting the custom Hook list.\n    //          * This doesn't happen during the module evaluation because we\n    //          * don't want to change the module order with inline requires.\n    //          * Next calls are noops. */\n    //   return <h1>Hi</h1>;\n    // }\n    //\n    // /* Call with arguments attaches the signature to the type: */\n    // _s(\n    //   Hello,\n    //   'useState{[foo, setFoo]}(0)',\n    //   () => [useCustomHook], /* Lazy to avoid triggering inline requires */\n    // );\n\n    function createSignatureFunctionForTransform() {\n      {\n        var savedType;\n        var hasCustomHooks;\n        var didCollectHooks = false;\n        return function (type, key, forceReset, getCustomHooks) {\n          if (typeof key === 'string') {\n            // We're in the initial phase that associates signatures\n            // with the functions. Note this may be called multiple times\n            // in HOC chains like _s(hoc1(_s(hoc2(_s(actualFunction))))).\n            if (!savedType) {\n              // We're in the innermost call, so this is the actual type.\n              savedType = type;\n              hasCustomHooks = typeof getCustomHooks === 'function';\n            } // Set the signature for all types (even wrappers!) in case\n            // they have no signatures of their own. This is to prevent\n            // problems like https://github.com/facebook/react/issues/20417.\n\n            if (type != null && (typeof type === 'function' || typeof type === 'object')) {\n              setSignature(type, key, forceReset, getCustomHooks);\n            }\n            return type;\n          } else {\n            // We're in the _s() call without arguments, which means\n            // this is the time to collect custom Hook signatures.\n            // Only do this once. This path is hot and runs *inside* every render!\n            if (!didCollectHooks && hasCustomHooks) {\n              didCollectHooks = true;\n              collectCustomHooksForSignature(savedType);\n            }\n          }\n        };\n      }\n    }\n    function isLikelyComponentType(type) {\n      {\n        switch (typeof type) {\n          case 'function':\n            {\n              // First, deal with classes.\n              if (type.prototype != null) {\n                if (type.prototype.isReactComponent) {\n                  // React class.\n                  return true;\n                }\n                var ownNames = Object.getOwnPropertyNames(type.prototype);\n                if (ownNames.length > 1 || ownNames[0] !== 'constructor') {\n                  // This looks like a class.\n                  return false;\n                } // eslint-disable-next-line no-proto\n\n                if (type.prototype.__proto__ !== Object.prototype) {\n                  // It has a superclass.\n                  return false;\n                } // Pass through.\n                // This looks like a regular function with empty prototype.\n              } // For plain functions and arrows, use name as a heuristic.\n\n              var name = type.name || type.displayName;\n              return typeof name === 'string' && /^[A-Z]/.test(name);\n            }\n          case 'object':\n            {\n              if (type != null) {\n                switch (getProperty(type, '$$typeof')) {\n                  case REACT_FORWARD_REF_TYPE:\n                  case REACT_MEMO_TYPE:\n                    // Definitely React components.\n                    return true;\n                  default:\n                    return false;\n                }\n              }\n              return false;\n            }\n          default:\n            {\n              return false;\n            }\n        }\n      }\n    }\n    exports._getMountedRootCount = _getMountedRootCount;\n    exports.collectCustomHooksForSignature = collectCustomHooksForSignature;\n    exports.createSignatureFunctionForTransform = createSignatureFunctionForTransform;\n    exports.findAffectedHostInstances = findAffectedHostInstances;\n    exports.getFamilyByID = getFamilyByID;\n    exports.getFamilyByType = getFamilyByType;\n    exports.hasUnrecoverableErrors = hasUnrecoverableErrors;\n    exports.injectIntoGlobalHook = injectIntoGlobalHook;\n    exports.isLikelyComponentType = isLikelyComponentType;\n    exports.performReactRefresh = performReactRefresh;\n    exports.register = register;\n    exports.setSignature = setSignature;\n  })();\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}