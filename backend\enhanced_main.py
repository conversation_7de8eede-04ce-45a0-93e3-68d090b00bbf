from fastapi import Fast<PERSON><PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import time
import uuid
import fitz  # PyMuPDF
import chromadb
from chromadb.config import Settings
from dotenv import load_dotenv
import openai
import asyncio
from pathlib import Path

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="No-Code/Low-Code Workflow Builder API",
    description="Enhanced API with full document processing and vector search",
    version="2.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
openai_api_key = os.getenv("OPENAI_API_KEY")
if openai_api_key:
    openai_client = openai.OpenAI(api_key=openai_api_key)
else:
    openai_client = None

# Initialize ChromaDB with new client configuration
try:
    chroma_client = chromadb.PersistentClient(path="./chroma_db")
    # Create or get collection
    collection = chroma_client.get_or_create_collection(
        name="documents",
        metadata={"hnsw:space": "cosine"}
    )
except Exception as e:
    print(f"ChromaDB initialization failed: {e}")
    chroma_client = None
    collection = None

# Create uploads directory
uploads_dir = Path("uploads")
uploads_dir.mkdir(exist_ok=True)

# In-memory storage for demo
workflows = []
documents = []
workflow_counter = 1
document_counter = 1

# Pydantic models
class WorkflowCreate(BaseModel):
    name: str
    description: Optional[str] = None

class Workflow(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    is_active: bool = True
    components: List[Dict[str, Any]] = []
    connections: List[Dict[str, Any]] = []

class ComponentCreate(BaseModel):
    component_type: str
    component_id: str
    name: str
    position_x: int = 0
    position_y: int = 0
    configuration: Dict[str, Any] = {}

class ConnectionCreate(BaseModel):
    connection_id: str
    source_component_id: int
    target_component_id: int

class ChatRequest(BaseModel):
    workflow_id: int
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    session_id: str
    message: str
    response: str
    execution_time: float
    component_results: Optional[Dict[str, Any]] = None

class Document(BaseModel):
    id: int
    filename: str
    content: str
    chunks: List[str] = []
    embeddings_generated: bool = False
    created_at: str

@app.get("/")
async def root():
    return {"message": "Enhanced No-Code/Low-Code Workflow Builder API"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy", 
        "openai_configured": openai_client is not None,
        "chromadb_configured": chroma_client is not None,
        "features": [
            "workflow_management",
            "document_processing", 
            "vector_search",
            "llm_integration",
            "chat_interface"
        ]
    }

# Workflow endpoints
@app.get("/api/workflows/", response_model=List[Workflow])
async def get_workflows():
    return workflows

@app.post("/api/workflows/", response_model=Workflow)
async def create_workflow(workflow: WorkflowCreate):
    global workflow_counter
    new_workflow = Workflow(
        id=workflow_counter,
        name=workflow.name,
        description=workflow.description,
        is_active=True,
        components=[],
        connections=[]
    )
    workflows.append(new_workflow)
    workflow_counter += 1
    return new_workflow

@app.get("/api/workflows/{workflow_id}", response_model=Workflow)
async def get_workflow(workflow_id: int):
    workflow = next((w for w in workflows if w.id == workflow_id), None)
    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")
    return workflow

@app.post("/api/workflows/{workflow_id}/components")
async def create_component(workflow_id: int, component: ComponentCreate):
    workflow = next((w for w in workflows if w.id == workflow_id), None)
    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")
    
    new_component = {
        "id": len(workflow.components) + 1,
        "workflow_id": workflow_id,
        "component_type": component.component_type,
        "component_id": component.component_id,
        "name": component.name,
        "position_x": component.position_x,
        "position_y": component.position_y,
        "configuration": component.configuration,
    }
    
    workflow.components.append(new_component)
    return new_component

@app.post("/api/workflows/{workflow_id}/connections")
async def create_connection(workflow_id: int, connection: ConnectionCreate):
    workflow = next((w for w in workflows if w.id == workflow_id), None)
    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")
    
    new_connection = {
        "id": len(workflow.connections) + 1,
        "workflow_id": workflow_id,
        "connection_id": connection.connection_id,
        "source_component_id": connection.source_component_id,
        "target_component_id": connection.target_component_id,
    }
    
    workflow.connections.append(new_connection)
    return new_connection

# Document endpoints
@app.post("/api/documents/upload")
async def upload_document(file: UploadFile = File(...)):
    global document_counter
    
    if not file.filename.endswith(('.pdf', '.txt')):
        raise HTTPException(status_code=400, detail="Only PDF and TXT files are supported")
    
    # Save file
    file_path = uploads_dir / f"{document_counter}_{file.filename}"
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Extract text
    try:
        if file.filename.endswith('.pdf'):
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
        else:  # .txt file
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
        
        # Create chunks (simple sentence-based chunking)
        chunks = [chunk.strip() for chunk in text.split('.') if chunk.strip()]
        
        # Create document record
        document = {
            "id": document_counter,
            "filename": file.filename,
            "content": text,
            "chunks": chunks,
            "embeddings_generated": False,
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        documents.append(document)
        
        # Generate embeddings if OpenAI is available
        if openai_client and collection:
            try:
                embeddings = []
                for chunk in chunks:
                    response = openai_client.embeddings.create(
                        model="text-embedding-ada-002",
                        input=chunk
                    )
                    embeddings.append(response.data[0].embedding)
                
                # Store in ChromaDB
                collection.add(
                    embeddings=embeddings,
                    documents=chunks,
                    metadatas=[{"document_id": document_counter, "chunk_index": i} for i in range(len(chunks))],
                    ids=[f"doc_{document_counter}_chunk_{i}" for i in range(len(chunks))]
                )
                
                document["embeddings_generated"] = True
                
            except Exception as e:
                print(f"Failed to generate embeddings: {e}")
        
        document_counter += 1
        return {"message": "Document uploaded successfully", "document_id": document["id"]}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to process document: {str(e)}")

@app.get("/api/documents/", response_model=List[Document])
async def get_documents():
    return documents

@app.post("/api/documents/search")
async def search_documents(query: str, top_k: int = 5, similarity_threshold: float = 0.7):
    if not openai_client or not collection:
        raise HTTPException(status_code=500, detail="Search not available - OpenAI or ChromaDB not configured")
    
    try:
        # Generate query embedding
        response = openai_client.embeddings.create(
            model="text-embedding-ada-002",
            input=query
        )
        query_embedding = response.data[0].embedding
        
        # Search in ChromaDB
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=top_k
        )
        
        search_results = []
        if results['documents']:
            for i, (doc, distance, metadata) in enumerate(zip(
                results['documents'][0], 
                results['distances'][0], 
                results['metadatas'][0]
            )):
                similarity = 1 - distance  # Convert distance to similarity
                if similarity >= similarity_threshold:
                    search_results.append({
                        "document_id": metadata['document_id'],
                        "chunk_index": metadata['chunk_index'],
                        "content": doc,
                        "similarity": similarity
                    })
        
        return search_results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

# Enhanced chat endpoint with workflow execution
@app.post("/api/chat/", response_model=ChatResponse)
async def chat(request: ChatRequest):
    start_time = time.time()
    
    # Find workflow
    workflow = next((w for w in workflows if w.id == request.workflow_id), None)
    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")
    
    session_id = request.session_id or str(uuid.uuid4())
    component_results = {}
    
    try:
        # Execute workflow components in order
        user_query = request.message
        context = ""
        
        # Check for knowledge base component
        kb_component = next((c for c in workflow.components if c['component_type'] == 'knowledge_base'), None)
        if kb_component and openai_client and collection:
            # Perform document search
            config = kb_component.get('configuration', {})
            top_k = config.get('top_k', 5)
            threshold = config.get('similarity_threshold', 0.7)
            
            search_results = await search_documents(user_query, top_k, threshold)
            if search_results:
                context = "\n".join([result['content'] for result in search_results])
                component_results['knowledge_base'] = {
                    "results_found": len(search_results),
                    "context_length": len(context)
                }
        
        # Check for LLM engine component
        llm_component = next((c for c in workflow.components if c['component_type'] == 'llm_engine'), None)
        if llm_component and openai_client:
            config = llm_component.get('configuration', {})
            model = config.get('model', 'gpt-3.5-turbo')
            max_tokens = config.get('max_tokens', 1000)
            temperature = config.get('temperature', 0.7)
            custom_prompt = config.get('custom_prompt', '')
            
            # Build prompt
            if custom_prompt and context:
                # Use custom prompt with context
                prompt = custom_prompt.replace('{query}', user_query).replace('{context}', context)
            elif custom_prompt:
                # Use custom prompt without context
                prompt = custom_prompt.replace('{query}', user_query)
            elif context:
                # Default prompt with context
                prompt = f"""Based on the following context, please answer the user's question accurately and helpfully.

Context:
{context}

User Question: {user_query}

Please provide a detailed and helpful answer based on the context provided."""
            else:
                # Default prompt without context
                prompt = f"Please answer the following question: {user_query}"
            
            # Generate response
            response = openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            ai_response = response.choices[0].message.content
            component_results['llm_engine'] = {
                "model": model,
                "tokens_used": response.usage.total_tokens
            }
        else:
            ai_response = f"Echo: {user_query} (Workflow executed but no LLM component configured)"
        
        execution_time = time.time() - start_time
        
        return ChatResponse(
            session_id=session_id,
            message=request.message,
            response=ai_response,
            execution_time=execution_time,
            component_results=component_results
        )
        
    except Exception as e:
        execution_time = time.time() - start_time
        return ChatResponse(
            session_id=session_id,
            message=request.message,
            response=f"Error executing workflow: {str(e)}",
            execution_time=execution_time,
            component_results=component_results
        )

# LLM endpoints
@app.get("/api/llm/models")
async def get_models():
    return {
        "openai": [
            "gpt-4",
            "gpt-4-turbo-preview", 
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ],
        "gemini": [
            "gemini-pro",
            "gemini-pro-vision"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
