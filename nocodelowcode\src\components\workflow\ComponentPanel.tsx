import React from 'react';
import { 
  MessageCircleIcon, 
  DatabaseIcon, 
  BrainIcon, 
  MonitorIcon 
} from 'lucide-react';

interface ComponentType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

const componentTypes: ComponentType[] = [
  {
    id: 'user_query',
    name: 'User Query',
    description: 'Accepts user input and serves as the entry point for the workflow',
    icon: <MessageCircleIcon className="w-6 h-6" />,
    color: 'bg-blue-500',
  },
  {
    id: 'knowledge_base',
    name: 'Knowledge Base',
    description: 'Searches through uploaded documents using vector similarity',
    icon: <DatabaseIcon className="w-6 h-6" />,
    color: 'bg-green-500',
  },
  {
    id: 'llm_engine',
    name: 'LLM Engine',
    description: 'Processes queries using AI language models like GPT or Gemini',
    icon: <BrainIcon className="w-6 h-6" />,
    color: 'bg-purple-500',
  },
  {
    id: 'output',
    name: 'Output',
    description: 'Displays the final response in a chat interface',
    icon: <MonitorIcon className="w-6 h-6" />,
    color: 'bg-orange-500',
  },
];

const ComponentPanel: React.FC = () => {
  const handleDragStart = (event: React.DragEvent, componentType: string) => {
    event.dataTransfer.setData('application/reactflow', componentType);
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <div className="p-4 space-y-4">
      {componentTypes.map((component) => (
        <div
          key={component.id}
          draggable
          onDragStart={(event) => handleDragStart(event, component.id)}
          className="group cursor-move bg-white border border-gray-200 rounded-lg p-4 hover:border-primary-300 hover:shadow-md transition-all duration-200"
        >
          <div className="flex items-start space-x-3">
            <div className={`${component.color} text-white p-2 rounded-lg flex-shrink-0`}>
              {component.icon}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-semibold text-gray-900 group-hover:text-primary-700">
                {component.name}
              </h3>
              <p className="text-xs text-gray-600 mt-1 leading-relaxed">
                {component.description}
              </p>
            </div>
          </div>
          
          {/* Drag indicator */}
          <div className="mt-3 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            </div>
          </div>
        </div>
      ))}

      {/* Instructions */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          How to use:
        </h4>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• Drag components to the workspace</li>
          <li>• Connect components with arrows</li>
          <li>• Configure each component's settings</li>
          <li>• Test your workflow with "Chat with Stack"</li>
        </ul>
      </div>

      {/* Component Guidelines */}
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          Workflow Guidelines:
        </h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Start with a User Query component</li>
          <li>• End with an Output component</li>
          <li>• Knowledge Base is optional but recommended</li>
          <li>• LLM Engine processes the final response</li>
        </ul>
      </div>
    </div>
  );
};

export default ComponentPanel;
