# No-Code/Low-Code Workflow Builder

A comprehensive web application that enables users to visually create and interact with intelligent workflows using drag-and-drop components.

## Features

### Core Components
- **User Query Component**: Entry point for user input
- **Knowledge Base Component**: Document upload, processing, and vector search
- **LLM Engine Component**: AI language model processing (OpenAI GPT, Gemini)
- **Output Component**: Final response display in chat interface

### Key Capabilities
- Visual workflow builder with React Flow
- Document processing with PyMuPDF and vector embeddings
- ChromaDB for vector storage and similarity search
- Real-time chat interface for workflow execution
- Web search integration with SerpAPI
- Containerized deployment with Docker

## Tech Stack

### Frontend
- React.js with TypeScript
- React Flow for drag-and-drop workflow builder
- Tailwind CSS for styling
- Axios for API communication

### Backend
- FastAPI with Python
- PostgreSQL database
- ChromaDB for vector storage
- OpenAI API for embeddings and LLM
- PyMuPDF for document text extraction

### Infrastructure
- Docker & Docker Compose
- Redis for caching
- Kubernetes manifests (optional)

## Quick Start

### Prerequisites
- <PERSON><PERSON> and Docker Compose
- OpenAI API key

### Environment Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd noCodeLowCode
```

2. Set up environment variables:
```bash
# Copy the example environment file
cp backend/.env.example backend/.env

# Edit the .env file with your API keys
# OPENAI_API_KEY is already configured with your provided key
```

### Running with Docker Compose

1. Start all services:
```bash
docker-compose up -d
```

2. Wait for services to be ready (about 30-60 seconds)

3. Access the application:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

### Manual Setup (Development)

#### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload
```

#### Frontend Setup
```bash
cd nocodelowcode
npm install
npm start
```

#### Database Setup
```bash
# Start PostgreSQL and ChromaDB
docker-compose up postgres chromadb redis -d
```

## Usage Guide

### 1. Create a Workflow
- Click "Create New Workflow" in the header
- Give your workflow a name and description

### 2. Build Your Workflow
- Drag components from the left panel to the workspace
- Connect components by dragging from output handles to input handles
- Configure each component by clicking on it

### 3. Configure Components

#### User Query Component
- Set placeholder text
- Configure validation rules

#### Knowledge Base Component
- Upload documents (PDF, TXT)
- Set similarity threshold
- Configure number of results

#### LLM Engine Component
- Choose model (GPT-3.5, GPT-4, Gemini)
- Set temperature and max tokens
- Add custom prompts
- Enable web search

#### Output Component
- Choose output format
- Set response templates

### 4. Test Your Workflow
- Click "Build Stack" to validate
- Click "Chat with Stack" to test
- Ask questions and see responses

## API Documentation

The backend provides RESTful APIs for:
- Workflow management (`/api/workflows/`)
- Document processing (`/api/documents/`)
- Chat interface (`/api/chat/`)
- LLM operations (`/api/llm/`)

Full API documentation is available at http://localhost:8000/docs

## Architecture

```
Frontend (React) ←→ Backend (FastAPI) ←→ PostgreSQL
                           ↓
                    ChromaDB (Vectors)
                           ↓
                    OpenAI API / Gemini
                           ↓
                    SerpAPI (Web Search)
```

## Development

### Project Structure
```
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── models/         # Database models
│   │   ├── routers/        # API endpoints
│   │   ├── services/       # Business logic
│   │   └── schemas/        # Pydantic schemas
│   └── requirements.txt
├── nocodelowcode/          # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API clients
│   │   └── types/          # TypeScript types
│   └── package.json
└── docker-compose.yml      # Container orchestration
```

### Adding New Components
1. Define component type in `types/index.ts`
2. Add component to `ComponentPanel.tsx`
3. Implement configuration in `ConfigurationPanel.tsx`
4. Add execution logic in `workflow_executor.py`

## Deployment

### Docker Deployment
```bash
# Build and run all services
docker-compose up -d

# Scale services
docker-compose up -d --scale backend=3
```

### Kubernetes Deployment (Optional)
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/
```

## Monitoring (Optional)

The application includes optional monitoring setup:
- Prometheus for metrics collection
- Grafana for dashboards
- ELK stack for logging

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL in .env

2. **ChromaDB Connection Error**
   - Verify ChromaDB service is running
   - Check CHROMA_HOST and CHROMA_PORT

3. **OpenAI API Errors**
   - Verify OPENAI_API_KEY is set correctly
   - Check API quota and billing

4. **Frontend Build Errors**
   - Run `npm install` to update dependencies
   - Check Node.js version (requires 16+)

### Logs
```bash
# View all service logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review API documentation at `/docs`
3. Create an issue in the repository
