// Workflow Types
export interface WorkflowComponent {
  id: number;
  workflow_id: number;
  component_type: 'user_query' | 'knowledge_base' | 'llm_engine' | 'output';
  component_id: string;
  name: string;
  position_x: number;
  position_y: number;
  configuration: Record<string, any>;
  created_at: string;
}

export interface WorkflowConnection {
  id: number;
  workflow_id: number;
  source_component_id: number;
  target_component_id: number;
  connection_id: string;
  created_at: string;
}

export interface Workflow {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  components: WorkflowComponent[];
  connections: WorkflowConnection[];
}

// React Flow Types
export interface FlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    label: string;
    componentType: string;
    configuration: Record<string, any>;
    dbId?: number;
  };
}

export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
  animated?: boolean;
}

// Component Configuration Types
export interface UserQueryConfig {
  placeholder?: string;
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
  };
}

export interface KnowledgeBaseConfig {
  top_k?: number;
  similarity_threshold?: number;
  document_ids?: number[];
}

export interface LLMEngineConfig {
  model: string;
  max_tokens?: number;
  temperature?: number;
  custom_prompt?: string;
  use_web_search?: boolean;
}

export interface OutputConfig {
  format?: 'text' | 'markdown' | 'json';
  template?: string;
}

// Document Types
export interface Document {
  id: number;
  filename: string;
  original_filename: string;
  file_path: string;
  file_size: number;
  mime_type?: string;
  is_processed: boolean;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  error_message?: string;
  created_at: string;
  updated_at?: string;
}

export interface DocumentChunk {
  id: number;
  document_id: number;
  chunk_index: number;
  content: string;
  page_number?: number;
  start_char?: number;
  end_char?: number;
  created_at: string;
}

export interface DocumentSearchResult {
  chunk_id: number;
  content: string;
  similarity_score: number;
  page_number?: number;
  document_filename: string;
}

// Chat Types
export interface ChatMessage {
  id: number;
  session_id: number;
  message_type: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: Record<string, any>;
  created_at: string;
}

export interface ChatSession {
  id: number;
  workflow_id: number;
  session_id: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  messages: ChatMessage[];
}

export interface ChatRequest {
  session_id?: string;
  workflow_id: number;
  message: string;
}

export interface ChatResponse {
  session_id: string;
  message: string;
  response: string;
  execution_time: number;
  component_results?: Record<string, any>;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: 'success' | 'error';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// UI State Types
export interface AppState {
  currentWorkflow?: Workflow;
  isLoading: boolean;
  error?: string;
  selectedComponent?: WorkflowComponent;
  chatSession?: ChatSession;
}

// Component Props Types
export interface ComponentPanelProps {
  onDragStart: (event: React.DragEvent, componentType: string) => void;
}

export interface WorkspaceProps {
  workflow?: Workflow;
  onWorkflowChange: (workflow: Workflow) => void;
}

export interface ConfigurationPanelProps {
  component?: WorkflowComponent;
  onConfigurationChange: (config: Record<string, any>) => void;
}

export interface ChatInterfaceProps {
  workflow: Workflow;
  onClose: () => void;
}
