{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\noCodeLowCode\\\\nocodelowcode\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport MainLayout from './components/layout/MainLayout';\nimport { useWorkflows } from './hooks/useWorkflows';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const {\n    workflows,\n    currentWorkflow,\n    isLoading,\n    createWorkflow,\n    selectWorkflow,\n    setCurrentWorkflow\n  } = useWorkflows();\n  const handleWorkflowCreate = async () => {\n    const name = prompt('Enter workflow name:');\n    if (!name) return;\n    const description = prompt('Enter workflow description (optional):');\n    try {\n      await createWorkflow(name, description || undefined);\n    } catch (error) {\n      // Error is already handled in the hook\n    }\n  };\n  const handleWorkflowSelect = workflow => {\n    selectWorkflow(workflow);\n  };\n  const handleWorkflowUpdate = updatedWorkflow => {\n    setCurrentWorkflow(updatedWorkflow);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading workflows...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(MainLayout, {\n      workflows: workflows,\n      currentWorkflow: currentWorkflow,\n      onWorkflowSelect: handleWorkflowSelect,\n      onWorkflowCreate: handleWorkflowCreate,\n      onWorkflowUpdate: handleWorkflowUpdate\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"LECHQwPO/3V2j552YA274ggeabI=\", false, function () {\n  return [useWorkflows];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "MainLayout", "useWorkflows", "jsxDEV", "_jsxDEV", "App", "_s", "workflows", "currentWorkflow", "isLoading", "createWorkflow", "selectWorkflow", "setCurrentWorkflow", "handleWorkflowCreate", "name", "prompt", "description", "undefined", "error", "handleWorkflowSelect", "workflow", "handleWorkflowUpdate", "updatedWorkflow", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onWorkflowSelect", "onWorkflowCreate", "onWorkflowUpdate", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/noCodeLowCode/nocodelowcode/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport MainLayout from './components/layout/MainLayout';\nimport { useWorkflows } from './hooks/useWorkflows';\nimport { Workflow } from './types';\n\nfunction App() {\n  const {\n    workflows,\n    currentWorkflow,\n    isLoading,\n    createWorkflow,\n    selectWorkflow,\n    setCurrentWorkflow,\n  } = useWorkflows();\n\n  const handleWorkflowCreate = async () => {\n    const name = prompt('Enter workflow name:');\n    if (!name) return;\n\n    const description = prompt('Enter workflow description (optional):');\n\n    try {\n      await createWorkflow(name, description || undefined);\n    } catch (error) {\n      // Error is already handled in the hook\n    }\n  };\n\n  const handleWorkflowSelect = (workflow: Workflow) => {\n    selectWorkflow(workflow);\n  };\n\n  const handleWorkflowUpdate = (updatedWorkflow: Workflow) => {\n    setCurrentWorkflow(updatedWorkflow);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading workflows...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"App\">\n      <MainLayout\n        workflows={workflows}\n        currentWorkflow={currentWorkflow}\n        onWorkflowSelect={handleWorkflowSelect}\n        onWorkflowCreate={handleWorkflowCreate}\n        onWorkflowUpdate={handleWorkflowUpdate}\n      />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,gCAAgC;AACvD,SAASC,YAAY,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGpD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IACJC,SAAS;IACTC,eAAe;IACfC,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC;EACF,CAAC,GAAGV,YAAY,CAAC,CAAC;EAElB,MAAMW,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMC,IAAI,GAAGC,MAAM,CAAC,sBAAsB,CAAC;IAC3C,IAAI,CAACD,IAAI,EAAE;IAEX,MAAME,WAAW,GAAGD,MAAM,CAAC,wCAAwC,CAAC;IAEpE,IAAI;MACF,MAAML,cAAc,CAACI,IAAI,EAAEE,WAAW,IAAIC,SAAS,CAAC;IACtD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd;IAAA;EAEJ,CAAC;EAED,MAAMC,oBAAoB,GAAIC,QAAkB,IAAK;IACnDT,cAAc,CAACS,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMC,oBAAoB,GAAIC,eAAyB,IAAK;IAC1DV,kBAAkB,CAACU,eAAe,CAAC;EACrC,CAAC;EAED,IAAIb,SAAS,EAAE;IACb,oBACEL,OAAA;MAAKmB,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEpB,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpB,OAAA;UAAKmB,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGxB,OAAA;UAAGmB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExB,OAAA;IAAKmB,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBpB,OAAA,CAACH,UAAU;MACTM,SAAS,EAAEA,SAAU;MACrBC,eAAe,EAAEA,eAAgB;MACjCqB,gBAAgB,EAAEV,oBAAqB;MACvCW,gBAAgB,EAAEjB,oBAAqB;MACvCkB,gBAAgB,EAAEV;IAAqB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACtB,EAAA,CArDQD,GAAG;EAAA,QAQNH,YAAY;AAAA;AAAA8B,EAAA,GART3B,GAAG;AAuDZ,eAAeA,GAAG;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}