#!/usr/bin/env python3
"""
Test the chat functionality
"""

import requests
import json

def test_chat():
    # First create a workflow
    workflow_data = {
        "name": "Test Chat Workflow",
        "description": "Testing chat functionality"
    }
    
    print("Creating workflow...")
    response = requests.post("http://localhost:8000/api/workflows/", json=workflow_data)
    if response.status_code != 200:
        print(f"Failed to create workflow: {response.text}")
        return
    
    workflow = response.json()
    workflow_id = workflow['id']
    print(f"Created workflow ID: {workflow_id}")
    
    # Test chat
    chat_data = {
        "workflow_id": workflow_id,
        "message": "Hello! Can you help me understand how workflow automation works?"
    }
    
    print("Sending chat message...")
    response = requests.post("http://localhost:8000/api/chat/", json=chat_data)
    if response.status_code != 200:
        print(f"Chat failed: {response.text}")
        return
    
    chat_response = response.json()
    print(f"Chat Response:")
    print(f"Session ID: {chat_response['session_id']}")
    print(f"Message: {chat_response['message']}")
    print(f"Response: {chat_response['response']}")
    print(f"Execution Time: {chat_response['execution_time']:.2f}s")
    
    # Test another message in the same session
    chat_data2 = {
        "workflow_id": workflow_id,
        "message": "What are the main components of a workflow?",
        "session_id": chat_response['session_id']
    }
    
    print("\nSending follow-up message...")
    response2 = requests.post("http://localhost:8000/api/chat/", json=chat_data2)
    if response2.status_code == 200:
        chat_response2 = response2.json()
        print(f"Follow-up Response: {chat_response2['response']}")
    
    print("\n✅ Chat functionality test completed!")

if __name__ == "__main__":
    test_chat()
