import React, { useState } from 'react';
import { Workflow } from '../../types';
import Header from './Header';
import ComponentPanel from '../workflow/ComponentPanel';
import WorkflowWorkspace from '../workflow/WorkflowWorkspace';
import ConfigurationPanel from '../workflow/ConfigurationPanel';
import ChatInterface from '../chat/ChatInterface';
import { Toaster } from 'react-hot-toast';

interface MainLayoutProps {
  workflows: Workflow[];
  currentWorkflow?: Workflow;
  onWorkflowSelect: (workflow: Workflow) => void;
  onWorkflowCreate: () => void;
  onWorkflowUpdate: (workflow: Workflow) => void;
}

const MainLayout: React.FC<MainLayoutProps> = ({
  workflows,
  currentWorkflow,
  onWorkflowSelect,
  onWorkflowCreate,
  onWorkflowUpdate,
}) => {
  const [selectedComponentId, setSelectedComponentId] = useState<string | null>(null);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isConfigPanelOpen, setIsConfigPanelOpen] = useState(false);

  const selectedComponent = currentWorkflow?.components.find(
    (comp) => comp.component_id === selectedComponentId
  );

  const handleComponentSelect = (componentId: string | null) => {
    setSelectedComponentId(componentId);
    setIsConfigPanelOpen(!!componentId);
  };

  const handleConfigurationChange = (config: Record<string, any>) => {
    if (selectedComponent && currentWorkflow) {
      const updatedComponents = currentWorkflow.components.map((comp) =>
        comp.component_id === selectedComponent.component_id
          ? { ...comp, configuration: config }
          : comp
      );

      const updatedWorkflow = {
        ...currentWorkflow,
        components: updatedComponents,
      };

      onWorkflowUpdate(updatedWorkflow);
    }
  };

  const handleBuildStack = () => {
    if (!currentWorkflow) {
      return;
    }
    // Validate workflow before enabling chat
    const hasUserQuery = currentWorkflow.components.some(
      (comp) => comp.component_type === 'user_query'
    );
    const hasOutput = currentWorkflow.components.some(
      (comp) => comp.component_type === 'output'
    );

    if (!hasUserQuery || !hasOutput) {
      alert('Workflow must have at least a User Query and Output component');
      return;
    }

    setIsChatOpen(true);
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <Header
        workflows={workflows}
        currentWorkflow={currentWorkflow}
        onWorkflowSelect={onWorkflowSelect}
        onWorkflowCreate={onWorkflowCreate}
        onBuildStack={handleBuildStack}
        onChatWithStack={() => setIsChatOpen(true)}
      />

      <div className="flex-1 flex overflow-hidden">
        {/* Component Library Panel */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Components</h2>
            <p className="text-sm text-gray-600 mt-1">
              Drag components to the workspace
            </p>
          </div>
          <div className="flex-1 overflow-y-auto">
            <ComponentPanel />
          </div>
        </div>

        {/* Main Workspace */}
        <div className="flex-1 flex flex-col">
          {currentWorkflow ? (
            <WorkflowWorkspace
              workflow={currentWorkflow}
              onWorkflowChange={onWorkflowUpdate}
              onComponentSelect={handleComponentSelect}
              selectedComponentId={selectedComponentId}
            />
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <svg
                    className="w-12 h-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Workflow Selected
                </h3>
                <p className="text-gray-600 mb-4">
                  Create a new workflow or select an existing one to get started
                </p>
                <button
                  onClick={onWorkflowCreate}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Create New Workflow
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Configuration Panel */}
        {isConfigPanelOpen && selectedComponent && (
          <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                Configuration
              </h2>
              <button
                onClick={() => setIsConfigPanelOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
            <div className="flex-1 overflow-y-auto">
              <ConfigurationPanel
                component={selectedComponent}
                onConfigurationChange={handleConfigurationChange}
              />
            </div>
          </div>
        )}
      </div>

      {/* Chat Interface Modal */}
      {isChatOpen && currentWorkflow && (
        <ChatInterface
          workflow={currentWorkflow}
          onClose={() => setIsChatOpen(false)}
        />
      )}

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
    </div>
  );
};

export default MainLayout;
