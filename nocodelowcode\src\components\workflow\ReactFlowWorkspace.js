import React, { useCallback, useRef, useState, useEffect } from 'react';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  ConnectionLineType,
  Panel,
  Handle,
  Position,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { ComponentMetadata } from '../../types';

// Custom node component with handles for connections
const CustomNode = ({ data, selected }) => {
  const getComponentColor = (type) => {
    const colors = {
      user_query: 'from-blue-500 to-blue-600',
      knowledge_base: 'from-green-500 to-green-600',
      llm_engine: 'from-purple-500 to-purple-600',
      output: 'from-orange-500 to-orange-600',
      web_search: 'from-indigo-500 to-indigo-600'
    };
    return colors[type] || 'from-gray-500 to-gray-600';
  };

  const getComponentIcon = (type) => {
    const icons = {
      user_query: '💬',
      knowledge_base: '📚',
      llm_engine: '🤖',
      output: '📤',
      web_search: '🔍'
    };
    return icons[type] || '⚙️';
  };

  const getBorderColor = (type) => {
    const colors = {
      user_query: 'border-blue-300',
      knowledge_base: 'border-green-300',
      llm_engine: 'border-purple-300',
      output: 'border-orange-300',
      web_search: 'border-indigo-300'
    };
    return colors[type] || 'border-gray-300';
  };

  return (
    <div className={`relative bg-white rounded-lg shadow-lg border-2 ${
      selected ? 'border-blue-500 shadow-blue-200' : getBorderColor(data.component_type)
    } min-w-[280px] transition-all duration-200 hover:shadow-xl`}>

      {/* Input Handle - Top */}
      {data.component_type !== 'user_query' && (
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-gray-400 border-2 border-white"
          style={{ top: -6 }}
        />
      )}

      {/* Header with gradient background */}
      <div className={`bg-gradient-to-r ${getComponentColor(data.component_type)} text-white p-3 rounded-t-lg`}>
        <div className="flex items-center space-x-3">
          <div className="text-xl">
            {getComponentIcon(data.component_type)}
          </div>
          <div className="flex-1">
            <div className="font-semibold text-sm">{data.name}</div>
            <div className="text-xs opacity-90 capitalize">
              {data.component_type.replace('_', ' ')}
            </div>
          </div>
          {Object.keys(data.configuration || {}).length > 0 && (
            <div className="bg-white bg-opacity-20 rounded-full p-1">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          )}
        </div>
      </div>

      {/* Content area */}
      <div className="p-3">
        <div className="text-xs text-gray-600 mb-2">
          {ComponentMetadata[data.component_type]?.description || 'Workflow component'}
        </div>

        {/* Configuration status */}
        <div className="flex items-center justify-between">
          <div className="text-xs text-gray-500">
            Status: {Object.keys(data.configuration || {}).length > 0 ? 'Configured' : 'Not configured'}
          </div>
          <div className="flex space-x-1">
            <button
              onClick={() => data.onSelect && data.onSelect()}
              className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded transition-colors"
            >
              Configure
            </button>
            <button
              onClick={() => data.onDelete && data.onDelete()}
              className="text-xs bg-red-100 hover:bg-red-200 text-red-600 px-2 py-1 rounded transition-colors"
            >
              Delete
            </button>
          </div>
        </div>
      </div>

      {/* Output Handle - Bottom */}
      {data.component_type !== 'output' && (
        <Handle
          type="source"
          position={Position.Bottom}
          className="w-3 h-3 bg-gray-400 border-2 border-white"
          style={{ bottom: -6 }}
        />
      )}
    </div>
  );
};

const nodeTypes = {
  custom: CustomNode,
};

const ReactFlowWorkspace = ({
  components,
  connections,
  onComponentSelect,
  onComponentDelete,
  onComponentAdd,
  onConnectionCreate,
  selectedComponentId,
  workflow
}) => {
  const reactFlowWrapper = useRef(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);

  // Auto-layout positions for better visual flow
  const getAutoPosition = (index, type) => {
    const positions = {
      user_query: { x: 100, y: 50 },
      knowledge_base: { x: 400, y: 50 },
      web_search: { x: 400, y: 200 },
      llm_engine: { x: 700, y: 125 },
      output: { x: 1000, y: 125 }
    };

    const basePosition = positions[type] || { x: 100 + (index * 300), y: 100 };
    return {
      x: basePosition.x + (Math.random() - 0.5) * 50, // Add slight randomness
      y: basePosition.y + (Math.random() - 0.5) * 50
    };
  };

  // Convert components to nodes
  useEffect(() => {
    const flowNodes = components.map((component, index) => ({
      id: component.component_id,
      type: 'custom',
      position: component.position_x && component.position_y
        ? { x: component.position_x, y: component.position_y }
        : getAutoPosition(index, component.component_type),
      data: {
        ...component,
        onDelete: () => onComponentDelete(component.component_id),
        onSelect: () => onComponentSelect(component.component_id),
      },
      selected: selectedComponentId === component.component_id,
    }));
    setNodes(flowNodes);
  }, [components, selectedComponentId, onComponentDelete, onComponentSelect, setNodes]);

  // Convert connections to edges with better styling
  useEffect(() => {
    const flowEdges = connections.map((connection, index) => {
      const sourceComponent = components.find(c => c.id === connection.source_component_id);
      const targetComponent = components.find(c => c.id === connection.target_component_id);

      // Color edges based on source component type
      const getEdgeColor = (sourceType) => {
        const colors = {
          user_query: '#3b82f6',
          knowledge_base: '#10b981',
          llm_engine: '#8b5cf6',
          web_search: '#6366f1',
          output: '#f59e0b'
        };
        return colors[sourceType] || '#6b7280';
      };

      return {
        id: connection.connection_id || `edge-${index}`,
        source: connection.source_component_id.toString(),
        target: connection.target_component_id.toString(),
        type: 'smoothstep',
        animated: true,
        style: {
          stroke: getEdgeColor(sourceComponent?.component_type),
          strokeWidth: 3,
          strokeDasharray: '5,5'
        },
        markerEnd: {
          type: 'arrowclosed',
          color: getEdgeColor(sourceComponent?.component_type),
        },
        label: `${sourceComponent?.name || 'Source'} → ${targetComponent?.name || 'Target'}`,
        labelStyle: {
          fontSize: 10,
          fontWeight: 600,
          fill: '#374151'
        },
        labelBgStyle: {
          fill: '#ffffff',
          fillOpacity: 0.8,
          rx: 4,
          ry: 4
        },
      };
    });
    setEdges(flowEdges);
  }, [connections, components, setEdges]);

  const onConnect = useCallback(
    (params) => {
      // Validate connection logic
      const sourceComponent = components.find(c => c.component_id === params.source);
      const targetComponent = components.find(c => c.component_id === params.target);

      if (!sourceComponent || !targetComponent) return;

      // Prevent self-connections
      if (params.source === params.target) {
        alert('Cannot connect a component to itself!');
        return;
      }

      // Check for existing connection
      const existingConnection = connections.find(
        conn => conn.source_component_id.toString() === params.source &&
                conn.target_component_id.toString() === params.target
      );

      if (existingConnection) {
        alert('Connection already exists!');
        return;
      }

      // Validate workflow logic
      const validConnections = {
        user_query: ['knowledge_base', 'web_search', 'llm_engine'],
        knowledge_base: ['llm_engine'],
        web_search: ['llm_engine'],
        llm_engine: ['output'],
        output: []
      };

      if (!validConnections[sourceComponent.component_type]?.includes(targetComponent.component_type)) {
        alert(`Cannot connect ${sourceComponent.component_type} to ${targetComponent.component_type}. Check workflow logic!`);
        return;
      }

      const newConnection = {
        connection_id: `conn_${Date.now()}`,
        source_component_id: parseInt(params.source),
        target_component_id: parseInt(params.target),
      };

      onConnectionCreate(newConnection);
    },
    [onConnectionCreate, components, connections]
  );

  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const componentType = event.dataTransfer.getData('application/reactflow');

      if (typeof componentType === 'undefined' || !componentType) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      // Check if component type already exists (for unique components)
      const uniqueComponents = ['user_query', 'output'];
      if (uniqueComponents.includes(componentType)) {
        const existingComponent = components.find(c => c.component_type === componentType);
        if (existingComponent) {
          alert(`Only one ${componentType.replace('_', ' ')} component is allowed per workflow!`);
          return;
        }
      }

      const newComponent = {
        component_id: `${componentType}_${Date.now()}`,
        component_type: componentType,
        name: ComponentMetadata[componentType]?.name || componentType,
        position_x: position.x,
        position_y: position.y,
        configuration: {},
      };

      onComponentAdd(newComponent);
    },
    [reactFlowInstance, onComponentAdd, components]
  );

  const onNodeClick = useCallback(
    (event, node) => {
      onComponentSelect(node.id);
    },
    [onComponentSelect]
  );

  const onNodeDragStop = useCallback(
    (event, node) => {
      // Update component position
      const component = components.find(c => c.component_id === node.id);
      if (component) {
        console.log('Node moved:', node.id, node.position);
        // You can add position update logic here if needed
      }
    },
    [components]
  );

  const connectionLineStyle = {
    strokeWidth: 3,
    stroke: '#3b82f6',
    strokeDasharray: '5,5',
  };

  const defaultEdgeOptions = {
    style: { strokeWidth: 3, stroke: '#3b82f6' },
    type: 'smoothstep',
    markerEnd: {
      type: 'arrowclosed',
      color: '#3b82f6',
    },
  };

  return (
    <div className="w-full h-full bg-gray-50" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={setReactFlowInstance}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        onNodeDragStop={onNodeDragStop}
        nodeTypes={nodeTypes}
        connectionLineType={ConnectionLineType.SmoothStep}
        connectionLineStyle={connectionLineStyle}
        defaultEdgeOptions={defaultEdgeOptions}
        fitView
        attributionPosition="bottom-left"
        proOptions={{ hideAttribution: true }}
        className="bg-gray-50"
      >
        <MiniMap
          nodeStrokeColor={(n) => {
            const nodeData = nodes.find(node => node.id === n.id)?.data;
            if (nodeData?.component_type === 'user_query') return '#3b82f6';
            if (nodeData?.component_type === 'knowledge_base') return '#10b981';
            if (nodeData?.component_type === 'llm_engine') return '#8b5cf6';
            if (nodeData?.component_type === 'output') return '#f59e0b';
            if (nodeData?.component_type === 'web_search') return '#6366f1';
            return '#6b7280';
          }}
          nodeColor={(n) => {
            const nodeData = nodes.find(node => node.id === n.id)?.data;
            if (nodeData?.component_type === 'user_query') return '#3b82f6';
            if (nodeData?.component_type === 'knowledge_base') return '#10b981';
            if (nodeData?.component_type === 'llm_engine') return '#8b5cf6';
            if (nodeData?.component_type === 'output') return '#f59e0b';
            if (nodeData?.component_type === 'web_search') return '#6366f1';
            return '#6b7280';
          }}
          nodeBorderRadius={8}
          className="bg-white border border-gray-200 rounded-lg"
        />
        <Controls
          className="bg-white border border-gray-200 rounded-lg shadow-lg"
          showZoom={true}
          showFitView={true}
          showInteractive={true}
        />
        <Background
          color="#e5e7eb"
          gap={20}
          size={1}
          variant="dots"
        />

        <Panel position="top-left" className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 m-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-sm font-bold">AI</span>
            </div>
            <div>
              <h3 className="font-bold text-gray-900 text-sm">
                {workflow?.name || 'AI Workflow Builder'}
              </h3>
              <div className="text-xs text-gray-600">
                {components.length} Components • {connections.length} Connections
              </div>
            </div>
          </div>

          {/* Workflow validation status */}
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="flex items-center space-x-2">
              {components.some(c => c.component_type === 'user_query') &&
               components.some(c => c.component_type === 'output') ? (
                <div className="flex items-center space-x-1 text-green-600">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-xs font-medium">Valid Workflow</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-amber-600">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <span className="text-xs font-medium">Incomplete</span>
                </div>
              )}
            </div>
          </div>
        </Panel>

        {components.length === 0 && (
          <Panel position="center" className="pointer-events-none">
            <div className="bg-white p-8 rounded-xl shadow-2xl text-center max-w-md border border-gray-200">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-3xl">🚀</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Build Your AI Workflow
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Drag components from the left panel and connect them to create powerful AI-driven workflows
              </p>
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-sm text-blue-800 font-medium mb-2">
                  💡 Quick Start Guide:
                </div>
                <div className="text-xs text-blue-700 space-y-1 text-left">
                  <div>1. Add a User Query component</div>
                  <div>2. Add Knowledge Base or Web Search</div>
                  <div>3. Add an LLM Engine component</div>
                  <div>4. Add an Output component</div>
                  <div>5. Connect them in order</div>
                </div>
              </div>
            </div>
          </Panel>
        )}
      </ReactFlow>
    </div>
  );
};

export default ReactFlowWorkspace;
